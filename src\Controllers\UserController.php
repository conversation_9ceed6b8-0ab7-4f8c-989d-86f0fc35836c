<?php

declare(strict_types=1);

namespace WeBot\Controllers;

use WeBot\Core\DIContainer;

/**
 * User Controller
 *
 * Handles user-related actions like registration, profile, etc.
 *
 * @package WeBot\Controllers
 * @version 2.0
 */
class UserController extends BaseController
{
    /**
     * Handle /start command
     */
    public function handleStart(array $message): array
    {
        $this->initialize(['message' => $message]);
        $this->logger->info('Start command received', ['user_id' => $this->fromId]);

        $user = $this->db->selectOne('users', ['userid' => $this->fromId]);

        if (!$user) {
            return $this->registerNewUser();
        }

        return $this->showMainMenu($user);
    }

    private function registerNewUser(): array
    {
        $this->db->insert('users', [
            'userid' => $this->fromId,
            'first_name' => $this->message['from']['first_name'] ?? '',
            'last_name' => $this->message['from']['last_name'] ?? '',
            'username' => $this->message['from']['username'] ?? '',
            'created_at' => date('Y-m-d H:i:s')
        ]);

        $this->logger->info('New user registered', ['user_id' => $this->fromId]);

        return $this->sendMessage('Welcome!');
    }

    private function showMainMenu(array $user): array
    {
        return $this->sendMessage("Welcome back, {$user['first_name']}!");
    }
}
