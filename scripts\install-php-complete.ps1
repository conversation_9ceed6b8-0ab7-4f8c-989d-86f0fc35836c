# Complete PHP Installation Script for WeBot
Write-Host "=== WeBot Complete PHP Installation ===" -ForegroundColor Green
Write-Host "Installing PHP 8.2+ and all dependencies..." -ForegroundColor Yellow
Write-Host ""

# Function to download file with progress
function Download-FileWithProgress {
    param(
        [string]$Url,
        [string]$OutputPath,
        [string]$Description
    )
    
    Write-Host "Downloading $Description..." -ForegroundColor Cyan
    try {
        $webClient = New-Object System.Net.WebClient
        $webClient.DownloadFile($Url, $OutputPath)
        Write-Host "✅ Downloaded: $Description" -ForegroundColor Green
        return $true
    } catch {
        Write-Host "❌ Failed to download: $Description" -ForegroundColor Red
        Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Create php directory
$phpDir = "php-runtime"
if (!(Test-Path $phpDir)) {
    New-Item -ItemType Directory -Path $phpDir -Force | Out-Null
}

# Method 1: Try to download PHP from official source
Write-Host "1. Attempting to download PHP 8.2..." -ForegroundColor Cyan

$phpUrls = @(
    "https://windows.php.net/downloads/releases/php-8.2.26-Win32-vs16-x64.zip",
    "https://windows.php.net/downloads/releases/php-8.2.25-Win32-vs16-x64.zip",
    "https://windows.php.net/downloads/releases/php-8.2.24-Win32-vs16-x64.zip"
)

$phpDownloaded = $false
foreach ($url in $phpUrls) {
    $phpZip = "$phpDir/php.zip"
    if (Download-FileWithProgress $url $phpZip "PHP 8.2") {
        $phpDownloaded = $true
        break
    }
}

if ($phpDownloaded) {
    Write-Host "2. Extracting PHP..." -ForegroundColor Cyan
    try {
        Expand-Archive -Path "$phpDir/php.zip" -DestinationPath $phpDir -Force
        Write-Host "✅ PHP extracted successfully" -ForegroundColor Green
        
        # Remove zip file
        Remove-Item "$phpDir/php.zip" -Force -ErrorAction SilentlyContinue
        
    } catch {
        Write-Host "❌ Failed to extract PHP: $($_.Exception.Message)" -ForegroundColor Red
        $phpDownloaded = $false
    }
}

# Method 2: If download failed, try Chocolatey with admin rights
if (-not $phpDownloaded) {
    Write-Host "2. Trying Chocolatey installation..." -ForegroundColor Cyan
    
    # Check if running as admin
    $isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")
    
    if ($isAdmin) {
        try {
            choco install php --version 8.2.26 -y --force
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✅ PHP installed via Chocolatey" -ForegroundColor Green
                $phpDownloaded = $true
            }
        } catch {
            Write-Host "❌ Chocolatey installation failed" -ForegroundColor Red
        }
    } else {
        Write-Host "⚠️ Not running as administrator, skipping Chocolatey" -ForegroundColor Yellow
    }
}

# Method 3: Create a portable PHP setup script
if (-not $phpDownloaded) {
    Write-Host "3. Creating manual installation guide..." -ForegroundColor Cyan
    
    $manualGuide = @"
# Manual PHP Installation Guide

Since automatic installation failed, please follow these steps:

## Option 1: Download PHP manually
1. Go to: https://windows.php.net/download/
2. Download PHP 8.2+ (Thread Safe, x64)
3. Extract to: $((Get-Location).Path)\php-runtime\
4. Run this script again

## Option 2: Use XAMPP
1. Download XAMPP from: https://www.apachefriends.org/
2. Install XAMPP
3. Add C:\xampp\php to your PATH
4. Run: php --version to verify

## Option 3: Use Chocolatey (as Administrator)
1. Open PowerShell as Administrator
2. Run: choco install php --version 8.2.26 -y
3. Run: php --version to verify

After PHP is installed, run:
php composer.phar install
"@
    
    $manualGuide | Out-File -FilePath "MANUAL_PHP_INSTALLATION.md" -Encoding UTF8
    Write-Host "✅ Created manual installation guide: MANUAL_PHP_INSTALLATION.md" -ForegroundColor Green
}

# Configure PHP if we have it
$phpExe = ""
$phpPaths = @(
    "$phpDir\php.exe",
    "C:\php\php.exe",
    "C:\xampp\php\php.exe",
    "C:\wamp64\bin\php\php8.2.0\php.exe",
    "php"
)

foreach ($path in $phpPaths) {
    try {
        if ($path -eq "php") {
            $null = & php --version 2>$null
            if ($LASTEXITCODE -eq 0) {
                $phpExe = "php"
                break
            }
        } elseif (Test-Path $path) {
            $null = & $path --version 2>$null
            if ($LASTEXITCODE -eq 0) {
                $phpExe = $path
                break
            }
        }
    } catch {
        continue
    }
}

if ($phpExe) {
    Write-Host "✅ Found PHP at: $phpExe" -ForegroundColor Green
    
    # Get PHP version
    $phpVersion = & $phpExe --version 2>$null | Select-Object -First 1
    Write-Host "PHP Version: $phpVersion" -ForegroundColor Gray
    
    # Configure PHP
    Write-Host "4. Configuring PHP..." -ForegroundColor Cyan
    
    $phpIniPath = Split-Path $phpExe -Parent
    $phpIni = Join-Path $phpIniPath "php.ini"
    
    if (!(Test-Path $phpIni)) {
        $phpIniDev = Join-Path $phpIniPath "php.ini-development"
        if (Test-Path $phpIniDev) {
            Copy-Item $phpIniDev $phpIni -Force
            Write-Host "✅ Created php.ini from development template" -ForegroundColor Green
        } else {
            # Create basic php.ini
            $basicIni = @"
; WeBot PHP Configuration
memory_limit = 512M
max_execution_time = 300
upload_max_filesize = 10M
post_max_size = 10M
date.timezone = Asia/Tehran

; Extensions
extension=curl
extension=fileinfo
extension=gd
extension=intl
extension=mbstring
extension=openssl
extension=pdo_mysql
extension=zip
extension=json

; Error reporting
display_errors = On
error_reporting = E_ALL
log_errors = On

; Security
allow_url_fopen = On
allow_url_include = Off
"@
            $basicIni | Out-File -FilePath $phpIni -Encoding UTF8
            Write-Host "✅ Created basic php.ini" -ForegroundColor Green
        }
    }
    
    # Test PHP extensions
    Write-Host "5. Testing PHP extensions..." -ForegroundColor Cyan
    $requiredExtensions = @('curl', 'json', 'mbstring', 'openssl', 'pdo')
    
    foreach ($ext in $requiredExtensions) {
        $result = & $phpExe -m 2>$null | Select-String $ext
        if ($result) {
            Write-Host "  ✅ $ext" -ForegroundColor Green
        } else {
            Write-Host "  ❌ $ext (missing)" -ForegroundColor Red
        }
    }
    
    # Install Composer dependencies
    Write-Host "6. Installing Composer dependencies..." -ForegroundColor Cyan
    
    if (Test-Path "composer.phar") {
        try {
            & $phpExe composer.phar install --no-interaction --prefer-dist
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✅ Dependencies installed successfully" -ForegroundColor Green
            } else {
                Write-Host "⚠️ Dependencies installation had issues" -ForegroundColor Yellow
            }
        } catch {
            Write-Host "❌ Failed to install dependencies: $($_.Exception.Message)" -ForegroundColor Red
        }
    } else {
        Write-Host "❌ composer.phar not found" -ForegroundColor Red
    }
    
    # Create helper scripts
    Write-Host "7. Creating helper scripts..." -ForegroundColor Cyan
    
    $phpBat = @"
@echo off
"$phpExe" %*
"@
    
    $composerBat = @"
@echo off
"$phpExe" composer.phar %*
"@
    
    $testBat = @"
@echo off
"$phpExe" test-runner.php
"@
    
    $phpBat | Out-File -FilePath "php.bat" -Encoding ASCII
    $composerBat | Out-File -FilePath "composer.bat" -Encoding ASCII
    $testBat | Out-File -FilePath "test.bat" -Encoding ASCII
    
    Write-Host "✅ Created helper scripts: php.bat, composer.bat, test.bat" -ForegroundColor Green
    
} else {
    Write-Host "❌ PHP not found. Please install PHP manually." -ForegroundColor Red
    Write-Host "See MANUAL_PHP_INSTALLATION.md for instructions." -ForegroundColor Yellow
}

# Summary
Write-Host ""
Write-Host "=== Installation Summary ===" -ForegroundColor Green

if ($phpExe) {
    Write-Host "✅ PHP is ready at: $phpExe" -ForegroundColor Green
    Write-Host ""
    Write-Host "Next steps:" -ForegroundColor Cyan
    Write-Host "1. Run: php.bat --version" -ForegroundColor White
    Write-Host "2. Run: composer.bat install" -ForegroundColor White
    Write-Host "3. Run: test.bat" -ForegroundColor White
    Write-Host "4. Fix syntax errors in PHP files" -ForegroundColor White
} else {
    Write-Host "❌ PHP installation incomplete" -ForegroundColor Red
    Write-Host "Please follow manual installation guide" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Installation completed at: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor Gray
Write-Host "=========================" -ForegroundColor Green
