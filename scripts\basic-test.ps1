# WeBot Basic Testing Script
# تست‌های اولیه بدون نیاز به PHP

Write-Host "=== WeBot Basic Testing Script ===" -ForegroundColor Green
Write-Host "شروع تست‌های اولیه پروژه..." -ForegroundColor Yellow
Write-Host ""

# تست 1: بررسی ساختار پروژه
Write-Host "1. بررسی ساختار پروژه:" -ForegroundColor Cyan

$requiredFiles = @(
    "composer.json",
    "src",
    "tests", 
    "config",
    "public",
    "migrations",
    "docs"
)

$missingFiles = @()
foreach ($file in $requiredFiles) {
    if (Test-Path $file) {
        Write-Host "✅ $file" -ForegroundColor Green
    } else {
        Write-Host "❌ $file (مفقود)" -ForegroundColor Red
        $missingFiles += $file
    }
}

if ($missingFiles.Count -eq 0) {
    Write-Host "✅ ساختار پروژه کامل است" -ForegroundColor Green
} else {
    Write-Host "❌ فایل‌های مفقود: $($missingFiles -join ', ')" -ForegroundColor Red
}
Write-Host ""

# تست 2: بررسی فایل composer.json
Write-Host "2. بررسی فایل composer.json:" -ForegroundColor Cyan

if (Test-Path "composer.json") {
    try {
        $composerContent = Get-Content "composer.json" -Raw | ConvertFrom-Json
        
        # بررسی نام پروژه
        if ($composerContent.name) {
            Write-Host "✅ نام پروژه: $($composerContent.name)" -ForegroundColor Green
        }
        
        # بررسی PHP version requirement
        if ($composerContent.require.php) {
            Write-Host "✅ نیازمندی PHP: $($composerContent.require.php)" -ForegroundColor Green
        }
        
        # بررسی dependencies
        $depCount = ($composerContent.require | Get-Member -MemberType NoteProperty).Count
        Write-Host "✅ تعداد dependencies: $depCount" -ForegroundColor Green
        
        # بررسی autoload
        if ($composerContent.autoload) {
            Write-Host "✅ Autoload تنظیم شده" -ForegroundColor Green
        }
        
    } catch {
        Write-Host "❌ خطا در خواندن composer.json: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "❌ فایل composer.json مفقود است" -ForegroundColor Red
}
Write-Host ""

# تست 3: بررسی فایل‌های تنظیمات
Write-Host "3. بررسی فایل‌های تنظیمات:" -ForegroundColor Cyan

if (Test-Path ".env.example") {
    Write-Host "✅ .env.example موجود است" -ForegroundColor Green
} else {
    Write-Host "❌ .env.example مفقود است" -ForegroundColor Red
}

if (Test-Path ".env") {
    Write-Host "✅ .env موجود است" -ForegroundColor Green
} else {
    Write-Host "⚠️ .env مفقود است (باید از .env.example کپی شود)" -ForegroundColor Yellow
}

if (Test-Path ".env.testing") {
    Write-Host "✅ .env.testing موجود است" -ForegroundColor Green
} else {
    Write-Host "⚠️ .env.testing مفقود است" -ForegroundColor Yellow
}
Write-Host ""

# تست 4: بررسی فایل‌های کلیدی
Write-Host "4. بررسی فایل‌های کلیدی:" -ForegroundColor Cyan

$keyFiles = @(
    "public/index.php",
    "public/webhook.php", 
    "src/Core/TelegramBot.php",
    "src/Core/Database.php",
    "src/Core/Config.php",
    "tests/Unit/BaseTestCase.php",
    "phpunit.xml"
)

foreach ($file in $keyFiles) {
    if (Test-Path $file) {
        $size = (Get-Item $file).Length
        Write-Host "✅ $file ($size bytes)" -ForegroundColor Green
    } else {
        Write-Host "❌ $file (مفقود)" -ForegroundColor Red
    }
}
Write-Host ""

# تست 5: بررسی migrations
Write-Host "5. بررسی فایل‌های Migration:" -ForegroundColor Cyan

if (Test-Path "migrations") {
    $migrationFiles = Get-ChildItem "migrations" -Filter "*.sql" | Sort-Object Name
    if ($migrationFiles.Count -gt 0) {
        Write-Host "✅ تعداد فایل‌های migration: $($migrationFiles.Count)" -ForegroundColor Green
        foreach ($migration in $migrationFiles) {
            Write-Host "  - $($migration.Name)" -ForegroundColor Gray
        }
    } else {
        Write-Host "⚠️ هیچ فایل migration یافت نشد" -ForegroundColor Yellow
    }
} else {
    Write-Host "❌ پوشه migrations مفقود است" -ForegroundColor Red
}
Write-Host ""

# تست 6: بررسی تست‌ها
Write-Host "6. بررسی فایل‌های تست:" -ForegroundColor Cyan

if (Test-Path "tests") {
    $testFiles = Get-ChildItem "tests" -Recurse -Filter "*Test.php"
    if ($testFiles.Count -gt 0) {
        Write-Host "✅ تعداد فایل‌های تست: $($testFiles.Count)" -ForegroundColor Green
        
        # گروه‌بندی تست‌ها
        $unitTests = $testFiles | Where-Object { $_.FullName -like "*Unit*" }
        $integrationTests = $testFiles | Where-Object { $_.FullName -like "*Integration*" }
        
        Write-Host "  - Unit Tests: $($unitTests.Count)" -ForegroundColor Gray
        Write-Host "  - Integration Tests: $($integrationTests.Count)" -ForegroundColor Gray
    } else {
        Write-Host "⚠️ هیچ فایل تست یافت نشد" -ForegroundColor Yellow
    }
} else {
    Write-Host "❌ پوشه tests مفقود است" -ForegroundColor Red
}
Write-Host ""

# تست 7: بررسی مستندات
Write-Host "7. بررسی مستندات:" -ForegroundColor Cyan

$docFiles = @(
    "README.md",
    "docs/TESTING_AND_DEBUGGING_GUIDE.md",
    "docs/API_DOCUMENTATION.md",
    "docs/DEPLOYMENT_GUIDE.md"
)

foreach ($doc in $docFiles) {
    if (Test-Path $doc) {
        Write-Host "✅ $doc" -ForegroundColor Green
    } else {
        Write-Host "❌ $doc (مفقود)" -ForegroundColor Red
    }
}
Write-Host ""

# تست 8: بررسی Docker files
Write-Host "8. بررسی فایل‌های Docker:" -ForegroundColor Cyan

$dockerFiles = @(
    "Dockerfile",
    "docker-compose.yml",
    "docker-compose.test.yml",
    "Dockerfile.test"
)

foreach ($dockerFile in $dockerFiles) {
    if (Test-Path $dockerFile) {
        Write-Host "✅ $dockerFile" -ForegroundColor Green
    } else {
        Write-Host "❌ $dockerFile (مفقود)" -ForegroundColor Red
    }
}
Write-Host ""

# خلاصه نتایج
Write-Host "=== خلاصه نتایج ===" -ForegroundColor Green
Write-Host "تست‌های اولیه پروژه WeBot تکمیل شد." -ForegroundColor Yellow
Write-Host ""
Write-Host "مراحل بعدی برای تست کامل:" -ForegroundColor Cyan
Write-Host "1. نصب PHP 8.2+" -ForegroundColor White
Write-Host "2. نصب Composer" -ForegroundColor White
Write-Host "3. اجرای 'composer install'" -ForegroundColor White
Write-Host "4. تنظیم فایل .env" -ForegroundColor White
Write-Host "5. اجرای 'vendor/bin/phpunit'" -ForegroundColor White
Write-Host ""
Write-Host "تاریخ و زمان تست: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor Gray
Write-Host "=========================" -ForegroundColor Green
