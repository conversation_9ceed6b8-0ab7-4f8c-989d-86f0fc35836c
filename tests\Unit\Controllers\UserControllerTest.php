<?php

declare(strict_types=1);

namespace WeBot\Tests\Unit\Controllers;

use WeBot\Tests\Unit\BaseTestCase;
use WeBot\Controllers\UserController;

/**
 * User Controller Unit Tests
 * 
 * Comprehensive tests for UserController functionality
 * 
 * @package WeBot\Tests\Unit\Controllers
 * @version 2.0
 */
class UserControllerTest extends BaseTestCase
{
    private ?UserController $controller = null;
    
    protected function setUp(): void
    {
        parent::setUp();
        $this->controller = $this->container->make(UserController::class);
    }
    
    /**
     * Test user registration with /start command
     */
    public function testHandleStartCommand(): void
    {
        $message = $this->createTestMessage(['text' => '/start']);

        $result = $this->controller->handleStart($message);

        // Check that TelegramService response is returned
        $this->assertArrayHasKey('ok', $result);
        $this->assertTrue($result['ok']);
        $this->assertArrayHasKey('result', $result);
        $this->assertArrayHasKey('message_id', $result['result']);
    }
    
    /**
     * Test user registration for new user
     */
    public function testNewUserRegistration(): void
    {
        $message = [
            'message_id' => 124,
            'from' => [
                'id' => 999888777,
                'username' => 'newuser',
                'first_name' => 'New User'
            ],
            'chat' => [
                'id' => 999888777,
                'type' => 'private'
            ],
            'text' => '/start',
            'date' => time()
        ];
        
        $result = $this->controller->handleStart($message);
        
        $this->assertArrayHasKey('method', $result);
        $this->assertEquals('sendMessage', $result['method']);
        $this->assertStringContains('ثبت نام', $result['text']);
        
        // Verify user was created in database
        $users = $this->database->query(
            "SELECT * FROM users WHERE telegram_id = ?",
            [999888777]
        );
        
        $this->assertEquals(1, count($users));
        $this->assertEquals('newuser', $users[0]['username']);
    }
    
    /**
     * Test banned user handling
     */
    public function testBannedUserHandling(): void
    {
        $message = [
            'message_id' => 125,
            'from' => [
                'id' => 555666777, // This is the banned user from test data
                'username' => 'banneduser',
                'first_name' => 'Banned User'
            ],
            'chat' => [
                'id' => 555666777,
                'type' => 'private'
            ],
            'text' => '/start',
            'date' => time()
        ];
        
        $result = $this->controller->handleStart($message);
        
        $this->assertArrayHasKey('method', $result);
        $this->assertEquals('sendMessage', $result['method']);
        $this->assertStringContains('مسدود', $result['text']);
    }
    
    /**
     * Test phone number request
     */
    public function testPhoneNumberRequest(): void
    {
        $message = [
            'message_id' => 126,
            'from' => [
                'id' => 987654321, // This is the pending user from test data
                'username' => 'testuser2',
                'first_name' => 'Test User 2'
            ],
            'chat' => [
                'id' => 987654321,
                'type' => 'private'
            ],
            'text' => '/start',
            'date' => time()
        ];
        
        $result = $this->controller->handleStart($message);
        
        $this->assertArrayHasKey('method', $result);
        $this->assertEquals('sendMessage', $result['method']);
        $this->assertStringContains('شماره تلفن', $result['text']);
    }
    
    /**
     * Test phone number validation
     */
    public function testPhoneNumberValidation(): void
    {
        // Test valid phone number
        $validMessage = [
            'message_id' => 127,
            'from' => [
                'id' => 987654321,
                'username' => 'testuser2',
                'first_name' => 'Test User 2'
            ],
            'chat' => [
                'id' => 987654321,
                'type' => 'private'
            ],
            'text' => '+1234567890',
            'date' => time()
        ];
        
        $result = $this->controller->handleMessage($validMessage);
        
        $this->assertArrayHasKey('method', $result);
        $this->assertEquals('sendMessage', $result['method']);
        $this->assertStringContains('تایید', $result['text']);
        
        // Test invalid phone number
        $invalidMessage = $validMessage;
        $invalidMessage['text'] = 'invalid_phone';
        
        $result = $this->controller->handleMessage($invalidMessage);
        
        $this->assertStringContains('نامعتبر', $result['text']);
    }
    
    /**
     * Test main menu display
     */
    public function testMainMenuDisplay(): void
    {
        $message = [
            'message_id' => 128,
            'from' => [
                'id' => 123456789, // This is the active user from test data
                'username' => 'testuser1',
                'first_name' => 'Test User 1'
            ],
            'chat' => [
                'id' => 123456789,
                'type' => 'private'
            ],
            'text' => '/menu',
            'date' => time()
        ];
        
        $result = $this->controller->handleMessage($message);
        
        $this->assertArrayHasKey('method', $result);
        $this->assertEquals('sendMessage', $result['method']);
        $this->assertArrayHasKey('reply_markup', $result);
        $this->assertStringContains('منوی اصلی', $result['text']);
    }
    
    /**
     * Test profile display
     */
    public function testProfileDisplay(): void
    {
        $callbackQuery = [
            'id' => 'callback_profile',
            'from' => [
                'id' => 123456789,
                'username' => 'testuser1',
                'first_name' => 'Test User 1'
            ],
            'message' => [
                'message_id' => 129,
                'chat' => ['id' => 123456789]
            ],
            'data' => 'profile'
        ];
        
        $result = $this->controller->handleCallback($callbackQuery);
        
        $this->assertArrayHasKey('method', $result);
        $this->assertEquals('editMessageText', $result['method']);
        $this->assertStringContains('پروفایل', $result['text']);
        $this->assertStringContains('testuser1', $result['text']);
    }
    
    /**
     * Test error handling for invalid input
     */
    public function testInvalidInputHandling(): void
    {
        $invalidMessage = [
            'message_id' => 130,
            'from' => [
                'id' => 123456789,
                'username' => 'testuser1',
                'first_name' => 'Test User 1'
            ],
            'chat' => [
                'id' => 123456789,
                'type' => 'private'
            ],
            'text' => '/nonexistent_command',
            'date' => time()
        ];
        
        $result = $this->controller->handleMessage($invalidMessage);
        
        $this->assertArrayHasKey('method', $result);
        $this->assertEquals('sendMessage', $result['method']);
        $this->assertStringContains('نامعتبر', $result['text']);
    }
    
    /**
     * Test rate limiting
     */
    public function testRateLimiting(): void
    {
        $message = [
            'message_id' => 131,
            'from' => [
                'id' => 123456789,
                'username' => 'testuser1',
                'first_name' => 'Test User 1'
            ],
            'chat' => [
                'id' => 123456789,
                'type' => 'private'
            ],
            'text' => '/start',
            'date' => time()
        ];
        
        // Send multiple rapid requests
        for ($i = 0; $i < 15; $i++) {
            $result = $this->controller->handleStart($message);
        }
        
        // The last request should be rate limited
        $this->assertStringContains('تعداد درخواست', $result['text']);
    }
}
