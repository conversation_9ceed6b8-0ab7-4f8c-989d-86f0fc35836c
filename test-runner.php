﻿#!/usr/bin/env php
<?php
/**
 * Basic Test Runner for WeBot
 * Runs basic syntax and structure tests
 */

echo "=== WeBot Basic Test Runner ===\n";
echo "Running basic tests...\n\n";

// Test 1: Check autoloader
echo "1. Testing autoloader:\n";
if (file_exists(__DIR__ . '/vendor/autoload.php')) {
    require_once __DIR__ . '/vendor/autoload.php';
    echo "   âœ… Autoloader loaded\n";
} else {
    echo "   âŒ Autoloader not found - run 'composer install'\n";
}

// Test 2: Check configuration
echo "\n2. Testing configuration:\n";
if (file_exists(__DIR__ . '/.env')) {
    echo "   âœ… .env file exists\n";
} else {
    echo "   âŒ .env file missing - copy from .env.example\n";
}

// Test 3: Check storage directories
echo "\n3. Testing storage directories:\n";
$dirs = ['storage', 'storage/logs', 'storage/cache', 'storage/sessions', 'storage/uploads'];
foreach ($dirs as $dir) {
    if (is_dir($dir) && is_writable($dir)) {
        echo "   âœ… $dir (writable)\n";
    } elseif (is_dir($dir)) {
        echo "   âš ï¸ $dir (not writable)\n";
    } else {
        echo "   âŒ $dir (missing)\n";
    }
}

// Test 4: Check core classes
echo "\n4. Testing core classes:\n";
$coreClasses = [
    'WeBot\\Core\\Config',
    'WeBot\\Core\\Database', 
    'WeBot\\Core\\TelegramBot'
];

foreach ($coreClasses as $class) {
    if (class_exists($class)) {
        echo "   âœ… $class\n";
    } else {
        echo "   âŒ $class (not found)\n";
    }
}

echo "\n=== Test Summary ===\n";
echo "Basic tests completed.\n";
echo "For full testing, run: vendor/bin/phpunit\n";
