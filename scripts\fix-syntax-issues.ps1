# WeBot Syntax Issues Fix Script
Write-Host "=== WeBot Syntax Issues Fix Script ===" -ForegroundColor Green
Write-Host "Fixing identified syntax issues..." -ForegroundColor Yellow
Write-Host ""

# Function to count brackets in a file
function Count-Brackets {
    param([string]$filePath)
    
    $content = Get-Content $filePath -Raw -ErrorAction SilentlyContinue
    if (-not $content) {
        return @{ Error = "Could not read file" }
    }
    
    $openBraces = ($content.ToCharArray() | Where-Object { $_ -eq '{' }).Count
    $closeBraces = ($content.ToCharArray() | Where-Object { $_ -eq '}' }).Count
    $openParens = ($content.ToCharArray() | Where-Object { $_ -eq '(' }).Count
    $closeParens = ($content.ToCharArray() | Where-Object { $_ -eq ')' }).Count
    $openSquare = ($content.ToCharArray() | Where-Object { $_ -eq '[' }).Count
    $closeSquare = ($content.ToCharArray() | Where-Object { $_ -eq ']' }).Count
    
    return @{
        OpenBraces = $openBraces
        CloseBraces = $closeBraces
        OpenParens = $openParens
        CloseParens = $closeParens
        OpenSquare = $openSquare
        CloseSquare = $closeSquare
        BracesMatch = ($openBraces -eq $closeBraces)
        ParensMatch = ($openParens -eq $closeParens)
        SquareMatch = ($openSquare -eq $closeSquare)
    }
}

# Files with syntax issues identified
$problematicFiles = @(
    "src/Core/InputValidator.php",
    "src/Core/SecurityManager.php", 
    "src/lib/index.php",
    "src/lib/qrconfig.php",
    "src/lib/qrrscode.php",
    "src/lib/merged_config.php",
    "src/Microservices/ApiGateway.php",
    "src/Middleware/InputValidationMiddleware.php",
    "src/Middleware/SecurityMiddleware.php",
    "src/Security/FileUploadValidator.php",
    "src/Services/MessageTemplateEngine.php"
)

Write-Host "1. Analyzing problematic files:" -ForegroundColor Cyan

foreach ($file in $problematicFiles) {
    if (Test-Path $file) {
        $analysis = Count-Brackets $file
        Write-Host "  File: $file" -ForegroundColor White
        
        if ($analysis.Error) {
            Write-Host "    Error: $($analysis.Error)" -ForegroundColor Red
            continue
        }
        
        Write-Host "    Braces: $($analysis.OpenBraces) open, $($analysis.CloseBraces) close" -ForegroundColor $(if ($analysis.BracesMatch) { "Green" } else { "Red" })
        Write-Host "    Parens: $($analysis.OpenParens) open, $($analysis.CloseParens) close" -ForegroundColor $(if ($analysis.ParensMatch) { "Green" } else { "Red" })
        Write-Host "    Square: $($analysis.OpenSquare) open, $($analysis.CloseSquare) close" -ForegroundColor $(if ($analysis.SquareMatch) { "Green" } else { "Red" })
        
        if (-not $analysis.BracesMatch) {
            $diff = $analysis.OpenBraces - $analysis.CloseBraces
            if ($diff -gt 0) {
                Write-Host "    Missing $diff closing brace(s)" -ForegroundColor Red
            } else {
                Write-Host "    Extra $([math]::Abs($diff)) closing brace(s)" -ForegroundColor Red
            }
        }
        
        if (-not $analysis.ParensMatch) {
            $diff = $analysis.OpenParens - $analysis.CloseParens
            if ($diff -gt 0) {
                Write-Host "    Missing $diff closing parenthesis(es)" -ForegroundColor Red
            } else {
                Write-Host "    Extra $([math]::Abs($diff)) closing parenthesis(es)" -ForegroundColor Red
            }
        }
        
        Write-Host ""
    } else {
        Write-Host "  File not found: $file" -ForegroundColor Yellow
    }
}

Write-Host "2. Creating missing directories:" -ForegroundColor Cyan

$requiredDirs = @(
    "storage",
    "storage/logs", 
    "storage/cache",
    "storage/sessions",
    "storage/uploads"
)

foreach ($dir in $requiredDirs) {
    if (!(Test-Path $dir)) {
        try {
            New-Item -ItemType Directory -Path $dir -Force | Out-Null
            Write-Host "  ✅ Created: $dir" -ForegroundColor Green
        } catch {
            Write-Host "  ❌ Failed to create: $dir - $($_.Exception.Message)" -ForegroundColor Red
        }
    } else {
        Write-Host "  ✅ Exists: $dir" -ForegroundColor Green
    }
}

Write-Host ""
Write-Host "3. Installing dependencies (if possible):" -ForegroundColor Cyan

# Check if we have composer.phar
if (Test-Path "composer.phar") {
    Write-Host "  Found composer.phar" -ForegroundColor Green
    
    # Try to find PHP
    $phpPaths = @(
        "php",
        "C:\php\php.exe",
        "C:\xampp\php\php.exe",
        "C:\wamp64\bin\php\php8.2.0\php.exe",
        "php-portable\php.exe"
    )
    
    $phpFound = $false
    foreach ($phpPath in $phpPaths) {
        try {
            $null = & $phpPath --version 2>$null
            Write-Host "  ✅ Found PHP at: $phpPath" -ForegroundColor Green
            
            # Try to install dependencies
            Write-Host "  Installing dependencies..." -ForegroundColor Yellow
            & $phpPath composer.phar install --no-interaction --prefer-dist 2>$null
            
            if ($LASTEXITCODE -eq 0) {
                Write-Host "  ✅ Dependencies installed successfully" -ForegroundColor Green
            } else {
                Write-Host "  ⚠️ Dependencies installation had issues" -ForegroundColor Yellow
            }
            
            $phpFound = $true
            break
        } catch {
            continue
        }
    }
    
    if (-not $phpFound) {
        Write-Host "  ❌ PHP not found in common locations" -ForegroundColor Red
        Write-Host "  Please install PHP 8.2+ manually" -ForegroundColor Yellow
    }
} else {
    Write-Host "  ❌ composer.phar not found" -ForegroundColor Red
}

Write-Host ""
Write-Host "4. Creating basic test runner:" -ForegroundColor Cyan

$testRunnerContent = @"
#!/usr/bin/env php
<?php
/**
 * Basic Test Runner for WeBot
 * Runs basic syntax and structure tests
 */

echo "=== WeBot Basic Test Runner ===\n";
echo "Running basic tests...\n\n";

// Test 1: Check autoloader
echo "1. Testing autoloader:\n";
if (file_exists(__DIR__ . '/vendor/autoload.php')) {
    require_once __DIR__ . '/vendor/autoload.php';
    echo "   ✅ Autoloader loaded\n";
} else {
    echo "   ❌ Autoloader not found - run 'composer install'\n";
}

// Test 2: Check configuration
echo "\n2. Testing configuration:\n";
if (file_exists(__DIR__ . '/.env')) {
    echo "   ✅ .env file exists\n";
} else {
    echo "   ❌ .env file missing - copy from .env.example\n";
}

// Test 3: Check storage directories
echo "\n3. Testing storage directories:\n";
`$dirs = ['storage', 'storage/logs', 'storage/cache', 'storage/sessions', 'storage/uploads'];
foreach (`$dirs as `$dir) {
    if (is_dir(`$dir) && is_writable(`$dir)) {
        echo "   ✅ `$dir (writable)\n";
    } elseif (is_dir(`$dir)) {
        echo "   ⚠️ `$dir (not writable)\n";
    } else {
        echo "   ❌ `$dir (missing)\n";
    }
}

// Test 4: Check core classes
echo "\n4. Testing core classes:\n";
`$coreClasses = [
    'WeBot\\Core\\Config',
    'WeBot\\Core\\Database', 
    'WeBot\\Core\\TelegramBot'
];

foreach (`$coreClasses as `$class) {
    if (class_exists(`$class)) {
        echo "   ✅ `$class\n";
    } else {
        echo "   ❌ `$class (not found)\n";
    }
}

echo "\n=== Test Summary ===\n";
echo "Basic tests completed.\n";
echo "For full testing, run: vendor/bin/phpunit\n";
"@

try {
    $testRunnerContent | Out-File -FilePath "test-runner.php" -Encoding UTF8
    Write-Host "  ✅ Created test-runner.php" -ForegroundColor Green
} catch {
    Write-Host "  ❌ Failed to create test-runner.php: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "=== Fix Summary ===" -ForegroundColor Green
Write-Host "Syntax analysis completed." -ForegroundColor White
Write-Host "Required directories created." -ForegroundColor White
Write-Host "Basic test runner created." -ForegroundColor White
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Cyan
Write-Host "1. Fix syntax issues in identified files" -ForegroundColor White
Write-Host "2. Install PHP 8.2+ if not available" -ForegroundColor White
Write-Host "3. Run: php composer.phar install" -ForegroundColor White
Write-Host "4. Run: php test-runner.php" -ForegroundColor White
Write-Host "5. Run: vendor/bin/phpunit" -ForegroundColor White
Write-Host ""
Write-Host "Fix completed at: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor Gray
Write-Host "=========================" -ForegroundColor Green
