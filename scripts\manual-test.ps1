# WeBot Manual Testing Script
Write-Host "=== WeBot Manual Testing Script ===" -ForegroundColor Green
Write-Host "Running manual tests without PHP..." -ForegroundColor Yellow
Write-Host ""

# Test 1: Project Structure Validation
Write-Host "1. Project Structure Validation:" -ForegroundColor Cyan

$projectStructure = @{
    "src/Core" = @("Config.php", "Database.php", "TelegramBot.php", "Logger.php")
    "src/Services" = @("UserService.php", "PanelService.php", "PaymentService.php")
    "src/Handlers" = @("MessageHandler.php", "CallbackHandler.php", "CommandHandler.php")
    "tests/Unit" = @("BaseTestCase.php")
    "tests/Integration" = @()
    "config" = @("database.php", "telegram.php", "panels.php")
    "migrations" = @()
    "public" = @("index.php", "webhook.php")
    "docs" = @("TESTING_AND_DEBUGGING_GUIDE.md")
}

$missingFiles = @()
$totalFiles = 0
$existingFiles = 0

foreach ($dir in $projectStructure.Keys) {
    Write-Host "  Checking $dir/:" -ForegroundColor Gray
    
    if (!(Test-Path $dir)) {
        Write-Host "    ❌ Directory missing: $dir" -ForegroundColor Red
        continue
    }
    
    foreach ($file in $projectStructure[$dir]) {
        $fullPath = "$dir/$file"
        $totalFiles++
        
        if (Test-Path $fullPath) {
            $size = (Get-Item $fullPath).Length
            Write-Host "    ✅ $file ($size bytes)" -ForegroundColor Green
            $existingFiles++
        } else {
            Write-Host "    ❌ $file (missing)" -ForegroundColor Red
            $missingFiles += $fullPath
        }
    }
}

$completeness = [math]::Round(($existingFiles / $totalFiles) * 100, 1)
Write-Host "  Project completeness: $completeness% ($existingFiles/$totalFiles files)" -ForegroundColor $(if ($completeness -gt 80) { "Green" } elseif ($completeness -gt 50) { "Yellow" } else { "Red" })
Write-Host ""

# Test 2: Configuration Files Analysis
Write-Host "2. Configuration Files Analysis:" -ForegroundColor Cyan

$configFiles = @{
    "composer.json" = "Project dependencies"
    ".env.example" = "Environment template"
    ".env" = "Environment configuration"
    ".env.testing" = "Testing environment"
    "phpunit.xml" = "PHPUnit configuration"
    "docker-compose.yml" = "Docker configuration"
    "Dockerfile" = "Docker build file"
}

foreach ($file in $configFiles.Keys) {
    if (Test-Path $file) {
        $content = Get-Content $file -Raw -ErrorAction SilentlyContinue
        $lines = ($content -split "`n").Count
        $size = (Get-Item $file).Length
        
        Write-Host "  ✅ $file ($lines lines, $size bytes) - $($configFiles[$file])" -ForegroundColor Green
        
        # Basic content validation
        if ($file -eq "composer.json") {
            try {
                $json = $content | ConvertFrom-Json
                if ($json.name) {
                    Write-Host "    Package: $($json.name)" -ForegroundColor Gray
                }
                if ($json.require.php) {
                    Write-Host "    PHP requirement: $($json.require.php)" -ForegroundColor Gray
                }
                $depCount = ($json.require | Get-Member -MemberType NoteProperty).Count
                Write-Host "    Dependencies: $depCount" -ForegroundColor Gray
            } catch {
                Write-Host "    Invalid JSON format" -ForegroundColor Yellow
            }
        }

        if ($file -eq ".env.example" -or $file -eq ".env") {
            $envVars = ($content -split "`n" | Where-Object { $_ -match "^[A-Z_]+=.*" }).Count
            Write-Host "    Environment variables: $envVars" -ForegroundColor Gray
        }
        
    } else {
        Write-Host "  ❌ $file (missing) - $($configFiles[$file])" -ForegroundColor Red
    }
}
Write-Host ""

# Test 3: Code Quality Analysis
Write-Host "3. Code Quality Analysis:" -ForegroundColor Cyan

$codeFiles = Get-ChildItem -Path "src" -Recurse -Filter "*.php" -ErrorAction SilentlyContinue
if ($codeFiles) {
    $totalLines = 0
    $totalSize = 0
    $classCount = 0
    $functionCount = 0
    
    foreach ($file in $codeFiles) {
        $content = Get-Content $file.FullName -Raw -ErrorAction SilentlyContinue
        if ($content) {
            $lines = ($content -split "`n").Count
            $totalLines += $lines
            $totalSize += $file.Length
            
            # Count classes and functions
            $classMatches = [regex]::Matches($content, "class\s+\w+")
            $functionMatches = [regex]::Matches($content, "function\s+\w+")
            
            $classCount += $classMatches.Count
            $functionCount += $functionMatches.Count
            
            Write-Host "  File $($file.Name): $lines lines, $($classMatches.Count) classes, $($functionMatches.Count) functions" -ForegroundColor Gray
        }
    }

    Write-Host "  Total: $($codeFiles.Count) files, $totalLines lines, $([math]::Round($totalSize/1024, 1)) KB" -ForegroundColor Green
    Write-Host "  Architecture: $classCount classes, $functionCount functions" -ForegroundColor Green
    Write-Host "  Average file size: $([math]::Round($totalLines/$codeFiles.Count, 1)) lines" -ForegroundColor Green
} else {
    Write-Host "  No PHP files found in src/" -ForegroundColor Red
}
}
Write-Host ""

# Test 4: Database Schema Analysis
Write-Host "4. Database Schema Analysis:" -ForegroundColor Cyan

if (Test-Path "migrations") {
    $migrationFiles = Get-ChildItem "migrations" -Filter "*.sql" | Sort-Object Name
    if ($migrationFiles.Count -gt 0) {
        Write-Host "  Migration files: $($migrationFiles.Count)" -ForegroundColor Green

        $totalTables = 0
        $totalIndexes = 0

        foreach ($migration in $migrationFiles) {
            $content = Get-Content $migration.FullName -Raw -ErrorAction SilentlyContinue
            if ($content) {
                $tables = [regex]::Matches($content, "CREATE TABLE", [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
                $indexes = [regex]::Matches($content, "CREATE INDEX", [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)

                $totalTables += $tables.Count
                $totalIndexes += $indexes.Count

                Write-Host "    File $($migration.Name): $($tables.Count) tables, $($indexes.Count) indexes" -ForegroundColor Gray
            }
        }

        Write-Host "  Total schema: $totalTables tables, $totalIndexes indexes" -ForegroundColor Green
    } else {
        Write-Host "  No migration files found" -ForegroundColor Yellow
    }
} else {
    Write-Host "  migrations/ directory not found" -ForegroundColor Red
}
}
Write-Host ""

# Test 5: Test Coverage Analysis
Write-Host "5. Test Coverage Analysis:" -ForegroundColor Cyan

if (Test-Path "tests") {
    $testFiles = Get-ChildItem "tests" -Recurse -Filter "*Test.php" -ErrorAction SilentlyContinue
    $unitTests = $testFiles | Where-Object { $_.FullName -like "*Unit*" }
    $integrationTests = $testFiles | Where-Object { $_.FullName -like "*Integration*" }
    
    Write-Host "  Total test files: $($testFiles.Count)" -ForegroundColor Green
    Write-Host "  Unit tests: $($unitTests.Count)" -ForegroundColor Green
    Write-Host "  Integration tests: $($integrationTests.Count)" -ForegroundColor Green

    if ($testFiles.Count -gt 0) {
        $totalTestMethods = 0
        foreach ($testFile in $testFiles) {
            $content = Get-Content $testFile.FullName -Raw -ErrorAction SilentlyContinue
            if ($content) {
                $testMethods = [regex]::Matches($content, "function\s+test\w+|function\s+\w+Test")
                $totalTestMethods += $testMethods.Count
            }
        }
        Write-Host "  Total test methods: $totalTestMethods" -ForegroundColor Green

        # Calculate test coverage estimate
        if ($codeFiles -and $testFiles.Count -gt 0) {
            $coverageRatio = [math]::Round(($testFiles.Count / $codeFiles.Count) * 100, 1)
            Write-Host "  Test coverage estimate: $coverageRatio% (test files vs source files)" -ForegroundColor $(if ($coverageRatio -gt 50) { "Green" } elseif ($coverageRatio -gt 25) { "Yellow" } else { "Red" })
        }
    }
} else {
    Write-Host "  tests/ directory not found" -ForegroundColor Red
}
}
Write-Host ""

# Test 6: Security Analysis
Write-Host "6. Security Analysis:" -ForegroundColor Cyan

$securityChecks = @{
    ".env in .gitignore" = (Test-Path ".gitignore") -and ((Get-Content ".gitignore" -ErrorAction SilentlyContinue) -contains ".env")
    "vendor/ in .gitignore" = (Test-Path ".gitignore") -and ((Get-Content ".gitignore" -ErrorAction SilentlyContinue) -contains "vendor/")
    "storage/ writable" = (Test-Path "storage") -and (Get-Item "storage" -ErrorAction SilentlyContinue).Attributes -notmatch "ReadOnly"
    "Secret token configured" = (Test-Path ".env.example") -and ((Get-Content ".env.example" -ErrorAction SilentlyContinue) -match "SECRET_TOKEN")
    "Database credentials template" = (Test-Path ".env.example") -and ((Get-Content ".env.example" -ErrorAction SilentlyContinue) -match "DB_PASSWORD")
}

foreach ($check in $securityChecks.Keys) {
    if ($securityChecks[$check]) {
        Write-Host "  ✅ $check" -ForegroundColor Green
    } else {
        Write-Host "  ❌ $check" -ForegroundColor Red
    }
}
Write-Host ""

# Test 7: Performance Analysis
Write-Host "7. Performance Analysis:" -ForegroundColor Cyan

# Check for performance-related configurations
$performanceChecks = @{
    "Caching configured" = (Test-Path ".env.example") -and ((Get-Content ".env.example" -ErrorAction SilentlyContinue) -match "CACHE")
    "Rate limiting" = (Test-Path ".env.example") -and ((Get-Content ".env.example" -ErrorAction SilentlyContinue) -match "RATE_LIMIT")
    "Memory limit set" = (Test-Path ".env.example") -and ((Get-Content ".env.example" -ErrorAction SilentlyContinue) -match "MEMORY_LIMIT")
    "Logging configured" = (Test-Path ".env.example") -and ((Get-Content ".env.example" -ErrorAction SilentlyContinue) -match "LOG_LEVEL")
}

foreach ($check in $performanceChecks.Keys) {
    if ($performanceChecks[$check]) {
        Write-Host "  ✅ $check" -ForegroundColor Green
    } else {
        Write-Host "  ⚠️ $check (not configured)" -ForegroundColor Yellow
    }
}
Write-Host ""

# Summary and Recommendations
Write-Host "=== Summary and Recommendations ===" -ForegroundColor Green

Write-Host "Project Status:" -ForegroundColor Cyan
Write-Host "  Completeness: $completeness%" -ForegroundColor White
Write-Host "  Files: $existingFiles/$totalFiles" -ForegroundColor White
Write-Host "  PHP files: $($codeFiles.Count)" -ForegroundColor White
Write-Host "  Test files: $($testFiles.Count)" -ForegroundColor White

Write-Host ""
Write-Host "Next Steps:" -ForegroundColor Cyan
Write-Host "1. Install PHP 8.2+ and Composer" -ForegroundColor White
Write-Host "2. Run: composer install" -ForegroundColor White
Write-Host "3. Configure .env file" -ForegroundColor White
Write-Host "4. Run database migrations" -ForegroundColor White
Write-Host "5. Execute PHPUnit tests" -ForegroundColor White
Write-Host "6. Set up webhook for Telegram bot" -ForegroundColor White

if ($missingFiles.Count -gt 0) {
    Write-Host ""
    Write-Host "Missing Files:" -ForegroundColor Red
    foreach ($file in $missingFiles) {
        Write-Host "  - $file" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "Manual testing completed at: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor Gray
Write-Host "=========================" -ForegroundColor Green
