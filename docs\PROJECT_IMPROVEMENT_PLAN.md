# برنامهٔ بهبود پروژه We-Bot

> نسخهٔ ۱.۰ – تاریخ: ۱۴۰۳/۰۴/۱۶

این سند خلاصه‌ای از مهم‌ترین پیشنهادها برای ارتقای کیفیت، امنیت و نگه‌داری پروژهٔ **We-Bot** است. هدف، تبدیل مخزن به یک سامانهٔ پایدار، مقیاس‌پذیر و منطبق با استانداردهای مدرن است.

---

## ۱. پاک‌سازی و یکپارچه‌سازی ساختار مخزن

| گام | توضیح | اولویت |
| --- | --- | :---: |
| ۱-۱ | انتقال یا حذف فایل‌ها و اسکریپت‌های Legacy موجود در ریشه (مثل `botapi.php`, `search.php`, …) به پوشهٔ `legacy/` یا حذف کامل | بالا |
| ۱-۲ | ادغام پوشه‌های تکراری (`docker`, `docker/`) و حذف فایل‌های زائد | بالا |
| ۱-۳ | ایجاد ساختار Feature-First در ‎`src/`‎ بر اساس DDD (Adapter, Service, Repository, Controller و …) | متوسط |
| ۱-۴ | افزودن README انگلیسی مختصر (`README.md`) همراه با لینک به `README-fa.md` | متوسط |

## ۲. استانداردسازی کد PHP

1. اعمال PSR-12: نصب `squizlabs/php_codesniffer` و تنظیم pre-commit hook برای اجرای `phpcs` و `phpcbf`.
2. افزودن **آنالیز ایستا** با `phpstan` یا `psalm` (سطح ۵ یا بالاتر).
3. استفاده از Autoload PSR-4 در `composer.json` و اجرای `composer dump-autoload`.
4. ماژولار کردن کلاس‌های بزرگ در ‎`Core/`‎ به چند کلاسِ کوچک‌تر (اصل SRP).

## ۳. تست و CI/CD

- فعال‌سازی GitHub Actions:
  ```yaml
  name: CI
  on: [push, pull_request]
  jobs:
    build:
      runs-on: ubuntu-latest
      services:
        mysql: …
      steps:
        - uses: actions/checkout@v4
        - name: Setup PHP
          uses: shivammathur/setup-php@v2
          with:
            php-version: '8.2'
            extensions: mbstring, pdo_mysql, intl
        - name: Install dependencies
          run: composer install --prefer-dist --no-progress --no-suggest
        - name: Static Analysis
          run: vendor/bin/phpstan analyse --memory-limit=1G
        - name: Tests
          run: vendor/bin/phpunit --coverage-text --colors=never | cat
  ```
- گزارش پوشش کد (>۸۰٪) و Badge در README.
- اضافه‌کردن Mutation Testing (اختیاری) با `infection/infection`.

## ۴. Docker و استقرار

1. ‌یکپارچه‌سازی تمام سرویس‌ها در یک `docker-compose.yml` مناسب توسعه و تولید.
2. استفاده از `.dockerignore` برای کاهش حجم Build Context.
3. افزودن Healthcheck برای کانتینرها (nginx، php-fpm، mysql، redis).
4. تعریف Stage تولید در Dockerfile (multi-stage build).

## ۵. امنیت

| موضوع | اقدام پیشنهادی |
| ----- | -------------- |
| Secrets | ذخیره در متغیرهای محیطی + GitHub Secrets + عدم کامیت کلیدها |
| Input Validation | اطمینان از استفادهٔ `InputValidator` و middleware در تمام مسیرها |
| Rate Limiting | فعال و تست کامل `RateLimiter.php` در APIهای حساس |
| SSL/TLS | بررسی تنظیمات `docker/nginx/ssl.conf` + صدور گواهی Let’s Encrypt |
| RLS دیتابیس | در صورت استفاده از PostgreSQL، فعال‌سازی Row-Level Security |

## ۶. پشتیبانی RTL و زبان فارسی

1. ایجاد لایهٔ Layout (مثلاً در `resources/views/layout.blade.php` یا معادل) با `<html lang="fa" dir="rtl">`.
2. افزودن TailwindCSS + افزونهٔ RTL (`tailwindcss-rtl`) و تنظیم کلاس‌های منطقی (`ms-*`, `me-*`, …).
3. جایگزین‌کردن فونت پیش‌فرض با **Vazirmatn** یا **IRANSans** در `tailwind.config.ts`.

## ۷. مستندسازی و داکیومنت اتوماتیک

- استفاده از **OpenAPI Generator** (`src/Documentation/OpenApiGenerator.php`) برای تولید `openapi.yaml`.
- انتشار مستندات API روی Swagger UI در مسیر `/docs/api` در زمان Build.
- افزودن شرح کلاس و متدها با PHPDoc.

## ۸. مانیتورینگ و لاگ

1. جایگزینی اسکریپت‌های دستی با **Prometheus + Grafana** یا SaaS (Sentry, New Relic).
2. استفاده از ساختار لاگ JSON و ارسال به ELK یا Loki.
3. افزودن Alert برای خطاهای سطح بحرانی (۵xx) و شاخص‌های عملکرد.

## ۹. نقشهٔ راه (Roadmap کوتاه‌مدت)

| فصل | تحویل‌های کلیدی |
| --- | --------------- |
| تیر ۱۴۰۳ | پاک‌سازی ریشه، PSR-12، GitHub Actions اولیه |
| مرداد ۱۴۰۳ | Docker یکپارچه، تست‌های ۸۰٪، مستندات Swagger آنلاین |
| شهریور ۱۴۰۳ | مانیتورینگ Prometheus، لاگ ساخت‌یافته، بهینه‌سازی امنیت |
| پاییز ۱۴۰۳ | مهاجرت جزئی به میکروسرویس با استفاده از `src/Microservices` |

---

> **توجه**: این برنامه قابلیت تغییر بر اساس نیازهای تیم و بازخورد مسیر توسعه را دارد. پیشنهاد می‌شود هر دو هفته یک بار در جلسهٔ Sprint Review وضعیت اجرای این برنامه مرور شود. 