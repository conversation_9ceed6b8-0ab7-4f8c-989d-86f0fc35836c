<?php

/**
 * Fix Logger Type Issues
 * 
 * This script fixes Logger type mismatches across the codebase
 */

declare(strict_types=1);

echo "🔧 Fixing Logger Type Issues...\n";

$files = [
    'src/Middleware/RateLimitMiddleware.php',
    'src/Core/SecurityManager.php',
    'src/Services/AuthService.php',
    'src/Services/PaymentService.php',
    'src/Services/PanelService.php',
    'src/Services/MessageService.php',
    'src/Services/TelegramService.php',
    'src/Services/DatabaseService.php',
    'src/Controllers/BaseController.php',
    'src/Controllers/UserController.php',
    'src/Controllers/AdminController.php',
    'src/Controllers/PaymentController.php',
    'src/Controllers/ServiceController.php',
];

$fixed = 0;
$errors = 0;

foreach ($files as $file) {
    if (!file_exists($file)) {
        echo "⚠️  File not found: $file\n";
        continue;
    }
    
    $content = file_get_contents($file);
    $originalContent = $content;
    
    // Check if file has Logger type issue
    if (strpos($content, 'private Logger $logger;') !== false || 
        strpos($content, 'protected Logger $logger;') !== false) {
        
        // Add Monolog import if not exists
        if (strpos($content, 'use Monolog\Logger as MonologLogger;') === false) {
            $content = str_replace(
                'use WeBot\Utils\Logger;',
                "use WeBot\Utils\Logger;\nuse Monolog\Logger as MonologLogger;",
                $content
            );
        }
        
        // Replace Logger type with MonologLogger
        $content = str_replace('private Logger $logger;', 'private MonologLogger $logger;', $content);
        $content = str_replace('protected Logger $logger;', 'protected MonologLogger $logger;', $content);
        
        if ($content !== $originalContent) {
            if (file_put_contents($file, $content)) {
                echo "✅ Fixed: $file\n";
                $fixed++;
            } else {
                echo "❌ Failed to write: $file\n";
                $errors++;
            }
        }
    } else {
        echo "ℹ️  No Logger issues: $file\n";
    }
}

echo "\n📊 Summary:\n";
echo "✅ Fixed: $fixed files\n";
echo "❌ Errors: $errors files\n";
echo "🎯 Total processed: " . count($files) . " files\n";

if ($fixed > 0) {
    echo "\n🚀 Logger type issues have been fixed!\n";
    echo "You can now run the tests again.\n";
}
