<?php

declare(strict_types=1);

namespace WeBot\Tests\Unit;

use WeBot\Models\User;
use WeBot\Models\Payment;
use WeBot\Models\Service;
use WeBot\Models\BaseModel;

/**
 * Model Unit Tests
 * 
 * Unit tests for all WeBot models including
 * User, Payment, Service, and BaseModel functionality.
 * 
 * @package WeBot\Tests\Unit
 * @version 2.0
 */
class ModelUnitTest
{
    private array $testResults = [];
    private int $totalTests = 0;
    private int $passedTests = 0;
    private int $failedTests = 0;
    private object $mockDatabase;

    public function __construct()
    {
        $this->setupMockDatabase();
    }

    /**
     * Setup mock database for testing
     */
    private function setupMockDatabase(): void
    {
        $this->mockDatabase = new class {
            private array $data = [];
            private int $lastInsertId = 1;

            public function fetchRow(string $sql, array $params = [], string $types = ''): ?array {
                return ['userid' => 123, 'first_name' => 'Test', 'wallet' => 50000];
            }

            public function fetchAll(string $sql, array $params = [], string $types = ''): array {
                return [['id' => 1, 'name' => 'Test']];
            }

            public function fetchValue(string $sql, array $params = [], string $types = '') {
                return 1;
            }

            public function insert(string $table, array $data): int {
                return $this->lastInsertId++;
            }

            public function update(string $table, array $data, array $where): int {
                return 1;
            }

            public function delete(string $table, array $where): int {
                return 1;
            }

            public function getAffectedRows(): int {
                return 1;
            }

            public function getLastInsertId(): int {
                return $this->lastInsertId - 1;
            }
        };
    }

    /**
     * Run all model unit tests
     */
    public function runAllTests(): array
    {
        echo "🧪 Model Unit Tests\n";
        echo "==================\n\n";

        $this->testBaseModelFunctionality();
        $this->testUserModel();
        $this->testPaymentModel();
        $this->testServiceModel();
        $this->testModelRelationships();
        $this->testModelValidation();
        $this->testModelCasting();

        return $this->getTestResults();
    }

    /**
     * Test BaseModel functionality
     */
    private function testBaseModelFunctionality(): void
    {
        $this->runTest('BaseModel Functionality', function() {
            // Create a test model extending BaseModel
            $testModel = new class($this->mockDatabase) extends BaseModel {
                protected string $table = 'test_table';
                protected array $fillable = ['name', 'value'];
                protected array $casts = ['value' => 'int'];
            };

            // Test attribute setting and getting
            $testModel->setAttribute('name', 'Test Name');
            if ($testModel->getAttribute('name') !== 'Test Name') {
                throw new \Exception('Attribute setting/getting failed');
            }

            // Test magic methods
            $testModel->name = 'Magic Test';
            if ($testModel->name !== 'Magic Test') {
                throw new \Exception('Magic methods failed');
            }

            // Test fillable
            $testModel->fill(['name' => 'Filled', 'value' => '123']);
            if ($testModel->name !== 'Filled' || $testModel->value !== 123) {
                throw new \Exception('Fill method failed');
            }

            // Test toArray
            $array = $testModel->toArray();
            if (!is_array($array) || !isset($array['name'])) {
                throw new \Exception('toArray method failed');
            }

            return true;
        });
    }

    /**
     * Test User model
     */
    private function testUserModel(): void
    {
        $this->runTest('User Model', function() {
            $user = new User($this->mockDatabase, [
                'userid' => 123,
                'first_name' => 'Test User',
                'last_name' => 'Last Name',
                'username' => 'testuser',
                'wallet' => 50000,
                'isAdmin' => false,
                'banned' => false
            ]);

            // Test basic properties
            if ($user->userid !== 123) {
                throw new \Exception('User ID property failed');
            }

            // Test wallet functionality
            if ($user->getWalletBalance() !== 50000) {
                throw new \Exception('Wallet balance getter failed');
            }

            if (!$user->hasEnoughBalance(30000)) {
                throw new \Exception('Balance check failed');
            }

            if ($user->hasEnoughBalance(60000)) {
                throw new \Exception('Balance check should fail for insufficient funds');
            }

            // Test admin status
            if ($user->isAdmin()) {
                throw new \Exception('Admin status should be false');
            }

            // Test ban status
            if ($user->isBanned()) {
                throw new \Exception('Ban status should be false');
            }

            // Test display name
            $displayName = $user->getDisplayName();
            if (empty($displayName)) {
                throw new \Exception('Display name should not be empty');
            }

            // Test full name
            $fullName = $user->getFullName();
            if ($fullName !== 'Test User Last Name') {
                throw new \Exception('Full name concatenation failed');
            }

            return true;
        });
    }

    /**
     * Test Payment model
     */
    private function testPaymentModel(): void
    {
        $this->runTest('Payment Model', function() {
            $payment = new Payment($this->mockDatabase, [
                'id' => 1,
                'user_id' => 123,
                'amount' => 50000,
                'gateway' => 'zarinpal',
                'status' => 'completed',
                'hash_id' => 'test_hash_123'
            ]);

            // Test basic properties
            if ($payment->id !== 1) {
                throw new \Exception('Payment ID property failed');
            }

            if ($payment->amount !== 50000) {
                throw new \Exception('Payment amount property failed');
            }

            // Test status methods
            if (!$payment->isCompleted()) {
                throw new \Exception('Payment should be completed');
            }

            if ($payment->isPending()) {
                throw new \Exception('Payment should not be pending');
            }

            if ($payment->isFailed()) {
                throw new \Exception('Payment should not be failed');
            }

            // Test gateway title
            $gatewayTitle = $payment->getGatewayTitle();
            if ($gatewayTitle !== 'زرین‌پال') {
                throw new \Exception('Gateway title translation failed');
            }

            // Test status title
            $statusTitle = $payment->getStatusTitle();
            if ($statusTitle !== 'موفق') {
                throw new \Exception('Status title translation failed');
            }

            // Test formatted amount
            $formattedAmount = $payment->getFormattedAmount();
            if (empty($formattedAmount)) {
                throw new \Exception('Amount formatting failed');
            }

            // Test status icon
            $statusIcon = $payment->getStatusIcon();
            if ($statusIcon !== '✅') {
                throw new \Exception('Status icon for completed payment should be ✅');
            }

            return true;
        });
    }

    /**
     * Test Service model
     */
    private function testServiceModel(): void
    {
        $this->runTest('Service Model', function() {
            $service = new Service($this->mockDatabase, [
                'id' => 1,
                'user_id' => 123,
                'server_id' => 1,
                'plan_id' => 1,
                'remark' => 'Test Service',
                'uuid' => 'test-uuid-123',
                'volume' => 10737418240, // 10GB
                'used_volume' => 1073741824, // 1GB
                'days' => 30,
                'status' => 'active',
                'expires_at' => date('Y-m-d H:i:s', time() + 86400) // Tomorrow
            ]);

            // Test basic properties
            if ($service->id !== 1) {
                throw new \Exception('Service ID property failed');
            }

            if ($service->user_id !== 123) {
                throw new \Exception('Service user_id property failed');
            }

            // Test status methods
            if (!$service->isActive()) {
                throw new \Exception('Service should be active');
            }

            if ($service->isExpired()) {
                throw new \Exception('Service should not be expired');
            }

            if ($service->isSuspended()) {
                throw new \Exception('Service should not be suspended');
            }

            // Test volume calculations
            $remainingVolume = $service->getRemainingVolume();
            if ($remainingVolume !== (10737418240 - 1073741824)) {
                throw new \Exception('Remaining volume calculation failed');
            }

            $usagePercentage = $service->getUsagePercentage();
            if ($usagePercentage !== 10.0) {
                throw new \Exception('Usage percentage calculation failed');
            }

            // Test formatted volumes
            $formattedVolume = $service->getFormattedVolume();
            if (empty($formattedVolume)) {
                throw new \Exception('Volume formatting failed');
            }

            $formattedUsed = $service->getFormattedUsedVolume();
            if (empty($formattedUsed)) {
                throw new \Exception('Used volume formatting failed');
            }

            // Test remaining days
            $remainingDays = $service->getRemainingDays();
            if ($remainingDays !== 1) {
                throw new \Exception('Remaining days calculation failed');
            }

            // Test status title and icon
            $statusTitle = $service->getStatusTitle();
            if ($statusTitle !== 'فعال') {
                throw new \Exception('Status title should be فعال');
            }

            $statusIcon = $service->getStatusIcon();
            if ($statusIcon !== '🟢') {
                throw new \Exception('Status icon for active service should be 🟢');
            }

            return true;
        });
    }

    /**
     * Test model relationships
     */
    private function testModelRelationships(): void
    {
        $this->runTest('Model Relationships', function() {
            // Test User -> Services relationship
            $user = new User($this->mockDatabase, ['userid' => 123]);
            $services = $user->getServices();
            if (!is_array($services)) {
                throw new \Exception('User services should return array');
            }

            // Test User -> Payments relationship
            $payments = $user->getPayments();
            if (!is_array($payments)) {
                throw new \Exception('User payments should return array');
            }

            // Test Payment -> User relationship
            $payment = new Payment($this->mockDatabase, ['user_id' => 123]);
            $paymentUser = $payment->getUser();
            // Note: This might return null in mock environment, which is acceptable

            // Test Service -> User relationship
            $service = new Service($this->mockDatabase, ['user_id' => 123]);
            $serviceUser = $service->getUser();
            // Note: This might return null in mock environment, which is acceptable

            return true;
        });
    }

    /**
     * Test model validation
     */
    private function testModelValidation(): void
    {
        $this->runTest('Model Validation', function() {
            // Test User validation
            $user = new User($this->mockDatabase);
            $user->fill([
                'userid' => 123,
                'first_name' => 'Valid User',
                'wallet' => 0
            ]);

            // Test that validation rules exist
            $reflection = new \ReflectionClass($user);
            $method = $reflection->getMethod('getValidationRules');
            $method->setAccessible(true);
            $rules = $method->invoke($user);

            if (!is_array($rules)) {
                throw new \Exception('Validation rules should be array');
            }

            // Test Payment validation
            $payment = new Payment($this->mockDatabase);
            $reflection = new \ReflectionClass($payment);
            $method = $reflection->getMethod('getValidationRules');
            $method->setAccessible(true);
            $rules = $method->invoke($payment);

            if (!is_array($rules)) {
                throw new \Exception('Payment validation rules should be array');
            }

            return true;
        });
    }

    /**
     * Test model casting
     */
    private function testModelCasting(): void
    {
        $this->runTest('Model Casting', function() {
            // Test integer casting
            $user = new User($this->mockDatabase);
            $user->setAttribute('wallet', '50000');
            if ($user->getAttribute('wallet') !== 50000) {
                throw new \Exception('Integer casting failed');
            }

            // Test boolean casting
            $user->setAttribute('isAdmin', '1');
            if ($user->getAttribute('isAdmin') !== true) {
                throw new \Exception('Boolean casting failed');
            }

            // Test array casting for Payment metadata
            $payment = new Payment($this->mockDatabase);
            $payment->setAttribute('metadata', '{"key": "value"}');
            $metadata = $payment->getAttribute('metadata');
            if (!is_array($metadata) || $metadata['key'] !== 'value') {
                throw new \Exception('JSON/Array casting failed');
            }

            return true;
        });
    }

    /**
     * Run individual test
     */
    private function runTest(string $testName, callable $test): void
    {
        $this->totalTests++;
        
        try {
            $result = $test();
            
            if ($result === true) {
                $this->passedTests++;
                $this->testResults[] = ['name' => $testName, 'status' => 'PASS', 'error' => null];
                echo "✅ {$testName}\n";
            } else {
                $this->failedTests++;
                $this->testResults[] = ['name' => $testName, 'status' => 'FAIL', 'error' => 'Test returned false'];
                echo "❌ {$testName}: Test returned false\n";
            }
        } catch (\Throwable $e) {
            $this->failedTests++;
            $this->testResults[] = ['name' => $testName, 'status' => 'FAIL', 'error' => $e->getMessage()];
            echo "❌ {$testName}: " . $e->getMessage() . "\n";
        }
    }

    /**
     * Get test results
     */
    private function getTestResults(): array
    {
        $successRate = $this->totalTests > 0 ? round(($this->passedTests / $this->totalTests) * 100, 2) : 0;
        
        return [
            'total_tests' => $this->totalTests,
            'passed_tests' => $this->passedTests,
            'failed_tests' => $this->failedTests,
            'success_rate' => $successRate,
            'results' => $this->testResults
        ];
    }

    /**
     * Print test summary
     */
    public function printSummary(): void
    {
        $results = $this->getTestResults();
        
        echo "\n📊 Model Unit Test Summary:\n";
        echo "==========================\n";
        echo "Total Tests: {$results['total_tests']}\n";
        echo "Passed: {$results['passed_tests']}\n";
        echo "Failed: {$results['failed_tests']}\n";
        echo "Success Rate: {$results['success_rate']}%\n";
        
        if ($results['failed_tests'] > 0) {
            echo "\n❌ Failed Tests:\n";
            foreach ($results['results'] as $result) {
                if ($result['status'] === 'FAIL') {
                    echo "  - {$result['name']}: {$result['error']}\n";
                }
            }
            echo "\n🔴 Model Unit Test: FAILED\n";
        } else {
            echo "\n🟢 Model Unit Test: PASSED\n";
            echo "\n🎉 Model unit tests completed successfully!\n";
        }
    }
}

// Run tests if executed directly
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'] ?? '')) {
    $tester = new ModelUnitTest();
    $tester->runAllTests();
    $tester->printSummary();
}
