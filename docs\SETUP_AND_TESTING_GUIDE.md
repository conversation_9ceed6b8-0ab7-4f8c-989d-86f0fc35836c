# راهنمای نصب و تست پروژه WeBot v2.0

## پیش‌نیازها

### 1. نصب PHP
```bash
# Windows (با Chocolatey)
choco install php

# یا دانلود مستقیم از:
# https://windows.php.net/download/

# Linux (Ubuntu/Debian)
sudo apt update
sudo apt install php8.2 php8.2-cli php8.2-mysql php8.2-curl php8.2-json php8.2-mbstring php8.2-xml php8.2-zip

# macOS (با Homebrew)
brew install php@8.2
```

### 2. نصب Composer
```bash
# Windows
# دانلود از: https://getcomposer.org/download/

# Linux/macOS
curl -sS https://getcomposer.org/installer | php
sudo mv composer.phar /usr/local/bin/composer
```

### 3. نصب MySQL/MariaDB
```bash
# Windows (با Chocolatey)
choco install mysql

# Linux (Ubuntu/Debian)
sudo apt install mysql-server

# macOS (با Homebrew)
brew install mysql
```

### 4. نصب Redis (اختیاری)
```bash
# Windows (با Chocolatey)
choco install redis-64

# Linux (Ubuntu/Debian)
sudo apt install redis-server

# macOS (با Homebrew)
brew install redis
```

## راه‌اندازی پروژه

### 1. کلون کردن پروژه
```bash
git clone <repository-url>
cd We-Bot
```

### 2. نصب Dependencies
```bash
# نصب dependencies PHP
composer install

# نصب dev dependencies برای تست
composer install --dev
```

### 3. تنظیم Environment
```bash
# کپی کردن فایل تنظیمات
cp .env.example .env

# ویرایش تنظیمات
nano .env
```

### 4. تنظیم دیتابیس
```bash
# ایجاد دیتابیس
mysql -u root -p
CREATE DATABASE webot_main;
CREATE DATABASE webot_test;
exit

# اجرای migrations
php scripts/run-migrations.php
```

## تست و دیباگ

### 1. اجرای تست‌های Unit
```bash
# اجرای همه تست‌ها
vendor/bin/phpunit

# اجرای تست‌های خاص
vendor/bin/phpunit tests/Unit/

# اجرای یک تست خاص
vendor/bin/phpunit tests/Unit/Services/QRCodeServiceTest.php

# اجرای تست با coverage
vendor/bin/phpunit --coverage-html coverage/
```

### 2. اجرای تست‌های Integration
```bash
# تنظیم محیط تست
export APP_ENV=testing

# اجرای تست‌های integration
vendor/bin/phpunit tests/Integration/

# تست API endpoints
vendor/bin/phpunit tests/Integration/API/
```

### 3. تست‌های Manual

#### تست Webhook
```bash
# استفاده از ngrok برای تست local
ngrok http 8000

# تنظیم webhook در Telegram
curl -X POST "https://api.telegram.org/bot<TOKEN>/setWebhook" \
     -H "Content-Type: application/json" \
     -d '{"url": "https://your-ngrok-url.ngrok.io/webhook"}'
```

#### تست Panel Connections
```bash
# تست اتصال Marzban
php scripts/test-panel-connection.php --panel=marzban

# تست اتصال Marzneshin
php scripts/test-panel-connection.php --panel=marzneshin

# تست اتصال X-UI
php scripts/test-panel-connection.php --panel=xui
```

### 4. دیباگ و Logging

#### فعال‌سازی Debug Mode
```bash
# در فایل .env
APP_DEBUG=true
LOG_LEVEL=debug
```

#### مشاهده Logs
```bash
# مشاهده logs در real-time
tail -f storage/logs/app.log

# مشاهده error logs
tail -f storage/logs/error.log

# مشاهده telegram logs
tail -f storage/logs/telegram.log
```

### 5. Performance Testing

#### Load Testing با Apache Bench
```bash
# تست basic load
ab -n 1000 -c 10 http://localhost:8000/

# تست webhook endpoint
ab -n 100 -c 5 -p webhook-payload.json -T application/json http://localhost:8000/webhook
```

#### Memory Usage Testing
```bash
# اجرای تست memory
php scripts/memory-test.php

# پروفایل memory با Xdebug
php -d xdebug.mode=profile scripts/profile-memory.php
```

## ابزارهای Development

### 1. Code Quality
```bash
# PHP CS Fixer
vendor/bin/php-cs-fixer fix

# PHPStan
vendor/bin/phpstan analyse

# PHPMD
vendor/bin/phpmd src text cleancode,codesize,controversial,design,naming,unusedcode
```

### 2. Security Testing
```bash
# Security Checker
composer audit

# Custom security tests
php scripts/security-check.php
```

### 3. Database Testing
```bash
# تست migrations
php scripts/test-migrations.php

# تست seeders
php scripts/test-seeders.php

# تست backup/restore
php scripts/test-backup.php
```

## مشکلات رایج و راه‌حل‌ها

### 1. مشکلات PHP
```bash
# خطای memory limit
php -d memory_limit=512M vendor/bin/phpunit

# خطای extension
sudo apt install php8.2-<extension-name>
```

### 2. مشکلات Database
```bash
# خطای connection
# بررسی تنظیمات در .env
# بررسی وضعیت MySQL service

# خطای migration
php scripts/reset-database.php
```

### 3. مشکلات Telegram
```bash
# خطای webhook
# بررسی SSL certificate
# بررسی firewall settings
# بررسی bot token
```

## CI/CD Pipeline

### GitHub Actions
```yaml
# .github/workflows/test.yml
name: Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: 8.2
      - name: Install dependencies
        run: composer install
      - name: Run tests
        run: vendor/bin/phpunit
```

## Docker Development

### استفاده از Docker
```bash
# ساخت image
docker build -t webot:dev .

# اجرای container
docker run -p 8000:80 webot:dev

# استفاده از docker-compose
docker-compose up -d

# اجرای تست در Docker
docker-compose exec app vendor/bin/phpunit
```

## نکات مهم

1. **همیشه تست‌ها را قبل از commit اجرا کنید**
2. **از محیط testing برای تست‌های خودکار استفاده کنید**
3. **Log files را به‌طور منظم بررسی کنید**
4. **Performance metrics را مانیتور کنید**
5. **Security updates را به‌روز نگه دارید**

## منابع اضافی

- [PHPUnit Documentation](https://phpunit.de/documentation.html)
- [Telegram Bot API](https://core.telegram.org/bots/api)
- [PHP Best Practices](https://www.php-fig.org/psr/)
- [Docker PHP Guide](https://docs.docker.com/language/php/)
