<?php

declare(strict_types=1);

namespace WeBot\Tests\Unit;

use P<PERSON>Unit\Framework\TestCase;
use PHPUnit\Framework\MockObject\MockObject;
use WeBot\Tests\Traits\MockHelperTrait;
use WeBot\Core\DIContainer;
use WeBot\Core\Database;

/**
 * Base Test Case
 * 
 * Base class for all unit tests with common functionality.
 * 
 * @package WeBot\Tests\Unit
 * @version 2.0
 */
abstract class BaseTestCase extends TestCase
{
    use MockHelperTrait;

    protected ?DIContainer $container = null;

    /**
     * Create a properly typed mock for an interface
     * 
     * @template T
     * @param class-string<T> $interfaceName
     * @return MockObject&T
     */
    protected function createInterfaceMock(string $interfaceName): MockObject
    {
        /** @var MockObject&T $mock */
        $mock = $this->createMock($interfaceName);
        return $mock;
    }

    /**
     * Create a properly typed mock for a class
     * 
     * @template T
     * @param class-string<T> $className
     * @return MockObject&T
     */
    protected function createClassMock(string $className): MockObject
    {
        /** @var MockObject&T $mock */
        $mock = $this->createMock($className);
        return $mock;
    }

    /**
     * Assert that a value is of expected type
     */
    protected function assertType(string $expectedType, $actual, string $message = ''): void
    {
        $actualType = gettype($actual);
        
        if ($expectedType === 'object' && is_object($actual)) {
            $actualType = get_class($actual);
        }
        
        $this->assertEquals($expectedType, $actualType, $message);
    }

    /**
     * Assert that an array has expected structure
     */
    protected function assertArrayStructure(array $expectedKeys, array $actual, string $message = ''): void
    {
        foreach ($expectedKeys as $key) {
            $this->assertArrayHasKey($key, $actual, $message . " - Missing key: $key");
        }
    }

    /**
     * Assert that a string contains a substring
     */
    protected function assertStringContains(string $needle, string $haystack, string $message = ''): void
    {
        $this->assertStringContainsString($needle, $haystack, $message);
    }

    /**
     * Assert that a string does not contain a substring
     */
    protected function assertStringNotContains(string $needle, string $haystack, string $message = ''): void
    {
        $this->assertStringNotContainsString($needle, $haystack, $message);
    }

    /**
     * Assert that a string contains multiple substrings
     */
    protected function assertStringContainsAll(array $needles, string $haystack, string $message = ''): void
    {
        foreach ($needles as $needle) {
            $this->assertStringContains($needle, $haystack, $message . " - Missing substring: $needle");
        }
    }

    /**
     * Create test data array
     */
    protected function createTestData(array $overrides = []): array
    {
        $defaults = [
            'id' => 1,
            'name' => 'Test Name',
            'email' => '<EMAIL>',
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        return array_merge($defaults, $overrides);
    }

    /**
     * Create test user data
     */
    protected function createTestUser(array $overrides = []): array
    {
        $defaults = [
            'telegram_id' => 123456789,
            'first_name' => 'Test',
            'last_name' => 'User',
            'username' => 'testuser',
            'phone' => '+1234567890',
            'status' => 'active',
            'phone_verified' => true
        ];

        return array_merge($defaults, $overrides);
    }

    /**
     * Create test message data
     */
    protected function createTestMessage(array $overrides = []): array
    {
        $defaults = [
            'message_id' => 1,
            'from' => [
                'id' => 123456789,
                'first_name' => 'Test',
                'username' => 'testuser'
            ],
            'chat' => [
                'id' => 123456789,
                'type' => 'private'
            ],
            'date' => time(),
            'text' => '/start'
        ];

        return array_merge($defaults, $overrides);
    }

    /**
     * Setup method - called before each test
     */
    protected function setUp(): void
    {
        parent::setUp();

        $this->container = new DIContainer();
        $this->container->setupDefaults();

        // Override database for testing
        $this->container->bind(Database::class, function() {
            return new Database([
                'default' => 'sqlite',
                'connections' => [
                    'sqlite' => [
                        'driver' => 'sqlite',
                        'database' => ':memory:',
                    ]
                ]
            ]);
        });

        // Bind array config for controllers
        $configArray = [
            'telegram' => [
                'bot_token' => 'test_token',
                'webhook_url' => 'https://test.com/webhook'
            ],
            'app' => [
                'name' => 'WeBot Test',
                'debug' => true
            ]
        ];

        $this->container->bind('config', function() use ($configArray) {
            return $configArray;
        });

        // Bind Config class for services - override default binding
        $this->container->bind(\WeBot\Core\Config::class, function() use ($configArray) {
            // Create a mock config that returns our test data
            $config = $this->createMock(\WeBot\Core\Config::class);
            $config->method('get')->willReturnCallback(function($key, $default = null) use ($configArray) {
                return $configArray[$key] ?? $default;
            });
            return $config;
        });

        // Bind Logger as singleton
        $this->container->bind(\WeBot\Utils\Logger::class, function() {
            return \WeBot\Utils\Logger::getInstance();
        });
    }

    /**
     * Teardown method - called after each test
     */
    protected function tearDown(): void
    {
        parent::tearDown();
        
        $this->container = null;
        
        // Clean up any test artifacts
        $this->cleanupTestArtifacts();
    }

    /**
     * Clean up test artifacts
     */
    private function cleanupTestArtifacts(): void
    {
        // Override in child classes if needed
    }

    /**
     * Skip test if condition is not met
     */
    protected function skipIf(bool $condition, string $message): void
    {
        if ($condition) {
            $this->markTestSkipped($message);
        }
    }

    /**
     * Skip test if extension is not loaded
     */
    protected function skipIfExtensionNotLoaded(string $extension): void
    {
        $this->skipIf(
            !extension_loaded($extension),
            "Extension '$extension' is not loaded"
        );
    }

    /**
     * Skip test if class does not exist
     */
    protected function skipIfClassNotExists(string $className): void
    {
        $this->skipIf(
            !class_exists($className),
            "Class '$className' does not exist"
        );
    }
}
