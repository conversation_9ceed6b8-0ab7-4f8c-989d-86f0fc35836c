<?php

require_once 'vendor/autoload.php';

use WeBot\Core\DIContainer;
use WeBot\Utils\Logger;

$container = new DIContainer();
$container->setupDefaults();

// Bind Logger as singleton (override)
$container->bind(Logger::class, function() {
    return Logger::getInstance();
});

echo "Bindings after setupDefaults and override:\n";
var_dump($container);

echo "\nTrying to make Logger:\n";
try {
    $logger = $container->make(Logger::class);
    echo "Success: " . get_class($logger) . "\n";
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}

echo "\nTrying to make BaseController dependencies:\n";
try {
    $config = $container->make(\WeBot\Core\Config::class);
    echo "Config: " . get_class($config) . "\n";
} catch (Exception $e) {
    echo "Config Error: " . $e->getMessage() . "\n";
}

try {
    $db = $container->make(\WeBot\Core\Database::class);
    echo "Database: " . get_class($db) . "\n";
} catch (Exception $e) {
    echo "Database Error: " . $e->getMessage() . "\n";
}

echo "\nTrying to make UserController:\n";
try {
    $controller = $container->make(\WeBot\Controllers\UserController::class);
    echo "Success: " . get_class($controller) . "\n";
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n";
    echo $e->getTraceAsString() . "\n";
}
