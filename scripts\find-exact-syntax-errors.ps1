# Find Exact Syntax Errors Script
param([string]$FilePath)

Write-Host "=== Finding Exact Syntax Errors ===" -ForegroundColor Green
Write-Host "Analyzing file: $FilePath" -ForegroundColor Yellow
Write-Host ""

if (!(Test-Path $FilePath)) {
    Write-Host "File not found: $FilePath" -ForegroundColor Red
    exit 1
}

$content = Get-Content $FilePath
$lineNumber = 0
$openParens = 0
$closeParens = 0
$openBraces = 0
$closeBraces = 0
$openSquare = 0
$closeSquare = 0

$issues = @()

foreach ($line in $content) {
    $lineNumber++
    
    # Skip full-line comments
    if ($line.Trim() -match '^//') {
        continue
    }
    
    # Remove inline comments (simple approach)
    $workingLine = $line
    if ($workingLine -match '//') {
        $commentPos = $workingLine.IndexOf('//')
        $workingLine = $workingLine.Substring(0, $commentPos)
    }
    
    # Remove strings (more careful approach)
    $inString = $false
    $stringChar = ''
    $cleanLine = ''
    
    for ($i = 0; $i -lt $workingLine.Length; $i++) {
        $char = $workingLine[$i]
        
        if (!$inString) {
            if ($char -eq '"' -or $char -eq "'") {
                $inString = $true
                $stringChar = $char
                $cleanLine += ' '  # Replace string content with space
            } else {
                $cleanLine += $char
            }
        } else {
            if ($char -eq $stringChar -and ($i -eq 0 -or $workingLine[$i-1] -ne '\')) {
                $inString = $false
                $stringChar = ''
                $cleanLine += ' '
            } else {
                $cleanLine += ' '  # Replace string content with space
            }
        }
    }
    
    # Count brackets in cleaned line
    $lineOpenParens = ($cleanLine.ToCharArray() | Where-Object { $_ -eq '(' }).Count
    $lineCloseParens = ($cleanLine.ToCharArray() | Where-Object { $_ -eq ')' }).Count
    $lineOpenBraces = ($cleanLine.ToCharArray() | Where-Object { $_ -eq '{' }).Count
    $lineCloseBraces = ($cleanLine.ToCharArray() | Where-Object { $_ -eq '}' }).Count
    $lineOpenSquare = ($cleanLine.ToCharArray() | Where-Object { $_ -eq '[' }).Count
    $lineCloseSquare = ($cleanLine.ToCharArray() | Where-Object { $_ -eq ']' }).Count
    
    $openParens += $lineOpenParens
    $closeParens += $lineCloseParens
    $openBraces += $lineOpenBraces
    $closeBraces += $lineCloseBraces
    $openSquare += $lineOpenSquare
    $closeSquare += $lineCloseSquare
    
    # Check for unbalanced brackets on this line
    if ($lineOpenParens -ne $lineCloseParens -or $lineOpenBraces -ne $lineCloseBraces -or $lineOpenSquare -ne $lineCloseSquare) {
        $issues += @{
            Line = $lineNumber
            Original = $line.Trim()
            Cleaned = $cleanLine.Trim()
            OpenParens = $lineOpenParens
            CloseParens = $lineCloseParens
            OpenBraces = $lineOpenBraces
            CloseBraces = $lineCloseBraces
            OpenSquare = $lineOpenSquare
            CloseSquare = $lineCloseSquare
            ParenBalance = $openParens - $closeParens
            BraceBalance = $openBraces - $closeBraces
            SquareBalance = $openSquare - $closeSquare
        }
    }
}

Write-Host "Final Counts:" -ForegroundColor Cyan
Write-Host "Parentheses: $openParens open, $closeParens close (diff: $($openParens - $closeParens))" -ForegroundColor $(if ($openParens -eq $closeParens) { "Green" } else { "Red" })
Write-Host "Braces: $openBraces open, $closeBraces close (diff: $($openBraces - $closeBraces))" -ForegroundColor $(if ($openBraces -eq $closeBraces) { "Green" } else { "Red" })
Write-Host "Square brackets: $openSquare open, $closeSquare close (diff: $($openSquare - $closeSquare))" -ForegroundColor $(if ($openSquare -eq $closeSquare) { "Green" } else { "Red" })
Write-Host ""

if ($issues.Count -gt 0) {
    Write-Host "Lines with unbalanced brackets:" -ForegroundColor Yellow
    Write-Host ""
    
    foreach ($issue in $issues) {
        Write-Host "Line $($issue.Line):" -ForegroundColor White
        Write-Host "  Original: $($issue.Original)" -ForegroundColor Gray
        Write-Host "  Cleaned:  $($issue.Cleaned)" -ForegroundColor DarkGray
        
        if ($issue.OpenParens -ne $issue.CloseParens) {
            Write-Host "  Parentheses: $($issue.OpenParens) open, $($issue.CloseParens) close (running balance: $($issue.ParenBalance))" -ForegroundColor Red
        }
        if ($issue.OpenBraces -ne $issue.CloseBraces) {
            Write-Host "  Braces: $($issue.OpenBraces) open, $($issue.CloseBraces) close (running balance: $($issue.BraceBalance))" -ForegroundColor Red
        }
        if ($issue.OpenSquare -ne $issue.CloseSquare) {
            Write-Host "  Square: $($issue.OpenSquare) open, $($issue.CloseSquare) close (running balance: $($issue.SquareBalance))" -ForegroundColor Red
        }
        Write-Host ""
    }
}

# Suggest specific fixes
if ($openParens -ne $closeParens -or $openBraces -ne $closeBraces -or $openSquare -ne $closeSquare) {
    Write-Host "Suggested fixes:" -ForegroundColor Cyan
    
    if ($openParens -ne $closeParens) {
        $diff = $openParens - $closeParens
        if ($diff -gt 0) {
            Write-Host "- Add $diff closing parenthesis ')' at the end of the file or appropriate locations" -ForegroundColor Yellow
        } else {
            Write-Host "- Remove $([math]::Abs($diff)) closing parenthesis ')'" -ForegroundColor Yellow
        }
    }
    
    if ($openBraces -ne $closeBraces) {
        $diff = $openBraces - $closeBraces
        if ($diff -gt 0) {
            Write-Host "- Add $diff closing brace '}' at the end of the file or appropriate locations" -ForegroundColor Yellow
        } else {
            Write-Host "- Remove $([math]::Abs($diff)) closing brace '}'" -ForegroundColor Yellow
        }
    }
    
    if ($openSquare -ne $closeSquare) {
        $diff = $openSquare - $closeSquare
        if ($diff -gt 0) {
            Write-Host "- Add $diff closing square bracket ']' at appropriate locations" -ForegroundColor Yellow
        } else {
            Write-Host "- Remove $([math]::Abs($diff)) closing square bracket ']'" -ForegroundColor Yellow
        }
    }
}

Write-Host ""
Write-Host "Analysis completed." -ForegroundColor Green
