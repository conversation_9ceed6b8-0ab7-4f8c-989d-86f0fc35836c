<?php

declare(strict_types=1);

namespace WeBot\Core;

/**
 * Input Validator
 *
 * Advanced input validation with custom rules,
 * sanitization, and security checks.
 *
 * @package WeBot\Core
 * @version 2.0
 */
class InputValidator
{
    private array $rules = [];
    private array $messages = [];
    private array $errors = [];

    public function __construct()
    {
        $this->initializeDefaultMessages();
    }

    /**
     * Validate data against rules
     */
    public function validate(array $data, array $rules): array
    {
        $this->rules = $rules;
        $this->errors = [];
        $sanitizedData = [];

        foreach ($rules as $field => $fieldRules) {
            $value = $data[$field] ?? null;
            $fieldErrors = [];

            // Parse rules
            $parsedRules = $this->parseRules($fieldRules);

            foreach ($parsedRules as $rule) {
                $result = $this->applyRule($field, $value, $rule);

                if (!$result['valid']) {
                    $fieldErrors = array_merge($fieldErrors, $result['errors']);
                }

                $value = $result['value']; // Use sanitized value
            }

            if (!empty($fieldErrors)) {
                $this->errors[$field] = $fieldErrors;
            }

            $sanitizedData[$field] = $value;
        }

        return [
            'valid' => empty($this->errors),
            'errors' => $this->errors,
            'data' => $sanitizedData
        ];
    }

    /**
     * Add custom validation rule
     */
    public function addRule(string $name, callable $validator, string $message = ''): void
    {
        $this->rules[$name] = $validator;

        if ($message) {
            $this->messages[$name] = $message;
        }
    }

    /**
     * Sanitize input value
     */
    public function sanitize($value, string $type = 'string')
    {
        switch ($type) {
            case 'string':
                return $this->sanitizeString($value);
            case 'email':
                return $this->sanitizeEmail($value);
            case 'url':
                return $this->sanitizeUrl($value);
            case 'int':
            case 'integer':
                return $this->sanitizeInteger($value);
            case 'float':
                return $this->sanitizeFloat($value);
            case 'boolean':
                return $this->sanitizeBoolean($value);
            case 'array':
                return $this->sanitizeArray($value);
            case 'json':
                return $this->sanitizeJson($value);
            default:
                return $value;
        }
    }

    /**
     * Validate specific field types
     */
    public function validateField(string $field, $value, string $type, array $options = []): array
    {
        $result = ['valid' => true, 'errors' => [], 'value' => $value];

        switch ($type) {
            case 'email':
                $result = $this->validateEmail($value);
                break;
            case 'phone':
                $result = $this->validatePhone($value, $options);
                break;
            case 'url':
                $result = $this->validateUrl($value);
                break;
            case 'date':
                $result = $this->validateDate($value, $options);
                break;
            case 'password':
                $result = $this->validatePassword($value, $options);
                break;
            case 'username':
                $result = $this->validateUsername($value, $options);
                break;
            case 'telegram_id':
                $result = $this->validateTelegramId($value);
                break;
            case 'amount':
                $result = $this->validateAmount($value, $options);
                break;
        }

        if (!$result['valid']) {
            $this->errors[$field] = $result['errors'];
        }

        return $result;
    }

    /**
     * Parse validation rules string
     */
    private function parseRules($rules): array
    {
        if (is_string($rules)) {
            return explode('|', $rules);
        }

        if (is_array($rules)) {
            return $rules;
        }

        return [];
    }

    /**
     * Apply single validation rule
     */
    private function applyRule(string $field, $value, string $rule): array
    {
        $result = ['valid' => true, 'errors' => [], 'value' => $value];

        // Security checks first
        if (is_string($value)) {
            if ($this->containsSqlInjection($value)) {
                $result['valid'] = false;
                $result['errors'][] = 'SQL injection attempt detected';
                return $result;
            }

            if ($this->containsXss($value)) {
                $result['valid'] = false;
                $result['errors'][] = 'XSS attempt detected';
                return $result;
            }
        }

        // Parse rule with parameters
        $ruleParts = explode(':', $rule, 2);
        $ruleName = $ruleParts[0];
        $parameters = isset($ruleParts[1]) ? explode(',', $ruleParts[1]) : [];

        switch ($ruleName) {
            case 'required':
                if (empty($value) && $value !== '0' && $value !== 0) {
                    $result['valid'] = false;
                    $result['errors'][] = $this->getMessage('required', $field);
                }
                break;

            case 'min':
                $min = (int)($parameters[0] ?? 0);
                if (strlen((string)$value) < $min) {
                    $result['valid'] = false;
                    $result['errors'][] = $this->getMessage('min', $field, ['min' => $min]);
                }
                break;

            case 'max':
                $max = (int)($parameters[0] ?? 255);
                if (strlen((string)$value) > $max) {
                    $result['valid'] = false;
                    $result['errors'][] = $this->getMessage('max', $field, ['max' => $max]);
                }
                break;

            case 'email':
                if (!empty($value) && !filter_var($value, FILTER_VALIDATE_EMAIL)) {
                    $result['valid'] = false;
                    $result['errors'][] = $this->getMessage('email', $field);
                }
                break;

            case 'numeric':
                if (!empty($value) && !is_numeric($value)) {
                    $result['valid'] = false;
                    $result['errors'][] = $this->getMessage('numeric', $field);
                }
                break;

            case 'integer':
                if (!empty($value) && !filter_var($value, FILTER_VALIDATE_INT)) {
                    $result['valid'] = false;
                    $result['errors'][] = $this->getMessage('integer', $field);
                }
                break;

            case 'url':
                if (!empty($value) && !filter_var($value, FILTER_VALIDATE_URL)) {
                    $result['valid'] = false;
                    $result['errors'][] = $this->getMessage('url', $field);
                }
                break;

            case 'regex':
                $pattern = $parameters[0] ?? '';
                if (!empty($value) && !preg_match($pattern, (string)$value)) {
                    $result['valid'] = false;
                    $result['errors'][] = $this->getMessage('regex', $field);
                }
                break;

            case 'in':
                if (!empty($value) && !in_array($value, $parameters)) {
                    $result['valid'] = false;
                    $result['errors'][] = $this->getMessage('in', $field, ['values' => implode(', ', $parameters)]);
                }
                break;

            case 'sanitize':
                $type = $parameters[0] ?? 'string';
                $result['value'] = $this->sanitize($value, $type);
                break;
        }

        return $result;
    }

    /**
     * Validate email address
     */
    public function validateEmail($value): array
    {
        $result = ['valid' => true, 'errors' => [], 'value' => $value];

        if (!empty($value)) {
            $sanitized = filter_var($value, FILTER_SANITIZE_EMAIL);
            $result['value'] = $sanitized;

            if (!filter_var($sanitized, FILTER_VALIDATE_EMAIL)) {
                $result['valid'] = false;
                $result['errors'][] = 'Invalid email format';
            }
        }

        return $result;
    }

    /**
     * Validate phone number
     */
    public function validatePhone($value, array $options = []): array
    {
        // Suppress unused parameter warning - reserved for future enhancements
        unset($options);

        $result = ['valid' => true, 'errors' => [], 'value' => $value];

        if (!empty($value)) {
            // Remove non-numeric characters except + and spaces
            $cleaned = preg_replace('/[^\d\+\s\-\(\)]/', '', (string)$value);
            $result['value'] = $cleaned;

            // Basic phone validation
            if (!preg_match('/^[\+]?[\d\s\-\(\)]{7,15}$/', $cleaned)) {
                $result['valid'] = false;
                $result['errors'][] = 'Invalid phone number format';
            }
        }

        return $result;
    }

    /**
     * Validate URL
     */
    public function validateUrl($value): array
    {
        $result = ['valid' => true, 'errors' => [], 'value' => $value];

        if (!empty($value)) {
            $sanitized = filter_var($value, FILTER_SANITIZE_URL);
            $result['value'] = $sanitized;

            if (!filter_var($sanitized, FILTER_VALIDATE_URL)) {
                $result['valid'] = false;
                $result['errors'][] = 'Invalid URL format';
            }
        }

        return $result;
    }

    /**
     * Validate date
     */
    private function validateDate($value, array $options = []): array
    {
        $result = ['valid' => true, 'errors' => [], 'value' => $value];
        $format = $options['format'] ?? 'Y-m-d';

        if (!empty($value)) {
            $date = \DateTime::createFromFormat($format, (string)$value);

            if (!$date || $date->format($format) !== $value) {
                $result['valid'] = false;
                $result['errors'][] = "Invalid date format. Expected: {$format}";
            }
        }

        return $result;
    }

    /**
     * Validate password
     */
    public function validatePassword($value, array $options = []): array
    {
        $result = ['valid' => true, 'errors' => [], 'value' => $value];

        if (!empty($value)) {
            $minLength = $options['min_length'] ?? 8;
            $requireSpecial = $options['require_special'] ?? false;
            $requireNumber = $options['require_number'] ?? false;
            $requireUpper = $options['require_upper'] ?? false;

            if (strlen($value) < $minLength) {
                $result['valid'] = false;
                $result['errors'][] = "Password must be at least {$minLength} characters";
            }

            if ($requireSpecial && !preg_match('/[!@#$%^&*(),.?":{}|<>]/', $value)) {
                $result['valid'] = false;
                $result['errors'][] = 'Password must contain special characters';
            }

            if ($requireNumber && !preg_match('/\d/', $value)) {
                $result['valid'] = false;
                $result['errors'][] = 'Password must contain numbers';
            }

            if ($requireUpper && !preg_match('/[A-Z]/', $value)) {
                $result['valid'] = false;
                $result['errors'][] = 'Password must contain uppercase letters';
            }
        }

        return $result;
    }

    /**
     * Validate username
     */
    private function validateUsername($value, array $options = []): array
    {
        $result = ['valid' => true, 'errors' => [], 'value' => $value];

        if (!empty($value)) {
            $minLength = $options['min_length'] ?? 3;
            $maxLength = $options['max_length'] ?? 30;

            if (!preg_match('/^[a-zA-Z0-9_]{' . $minLength . ',' . $maxLength . '}$/', (string)$value)) {
                $result['valid'] = false;
                $result['errors'][] = "Username must be {$minLength}-{$maxLength} characters, alphanumeric and underscore only";
            }
        }

        return $result;
    }

    /**
     * Validate Telegram ID
     */
    private function validateTelegramId($value): array
    {
        $result = ['valid' => true, 'errors' => [], 'value' => $value];

        if (!empty($value)) {
            if (!preg_match('/^\d{5,12}$/', (string)$value)) {
                $result['valid'] = false;
                $result['errors'][] = 'Invalid Telegram ID format';
            }
        }

        return $result;
    }

    /**
     * Validate amount/money
     */
    private function validateAmount($value, array $options = []): array
    {
        $result = ['valid' => true, 'errors' => [], 'value' => $value];

        if (!empty($value)) {
            $min = $options['min'] ?? 0;
            $max = $options['max'] ?? PHP_INT_MAX;

            if (!is_numeric($value)) {
                $result['valid'] = false;
                $result['errors'][] = 'Amount must be numeric';
            } elseif ($value < $min) {
                $result['valid'] = false;
                $result['errors'][] = "Amount must be at least {$min}";
            } elseif ($value > $max) {
                $result['valid'] = false;
                $result['errors'][] = "Amount cannot exceed {$max}";
            } else {
                $result['value'] = (float)$value;
            }
        }

        return $result;
    }

    /**
     * Sanitization methods
     */
    private function sanitizeString($value): string
    {
        return htmlspecialchars(trim((string)$value), ENT_QUOTES | ENT_HTML5, 'UTF-8');
    }

    private function sanitizeEmail($value): string
    {
        return filter_var(trim((string)$value), FILTER_SANITIZE_EMAIL);
    }

    private function sanitizeUrl($value): string
    {
        return filter_var(trim((string)$value), FILTER_SANITIZE_URL);
    }

    private function sanitizeInteger($value): int
    {
        return (int)filter_var($value, FILTER_SANITIZE_NUMBER_INT);
    }

    private function sanitizeFloat($value): float
    {
        return (float)filter_var($value, FILTER_SANITIZE_NUMBER_FLOAT, FILTER_FLAG_ALLOW_FRACTION);
    }

    private function sanitizeBoolean($value): bool
    {
        return filter_var($value, FILTER_VALIDATE_BOOLEAN);
    }

    private function sanitizeArray($value): array
    {
        if (!is_array($value)) {
            return [];
        }

        return array_map([$this, 'sanitizeString'], $value);
    }

    private function sanitizeJson($value): ?array
    {
        if (is_string($value)) {
            $decoded = json_decode($value, true);
            return json_last_error() === JSON_ERROR_NONE ? $decoded : null;
        }

        return is_array($value) ? $value : null;
    }

    /**
     * Get validation message
     */
    private function getMessage(string $rule, string $field, array $params = []): string
    {
        $message = $this->messages[$rule] ?? "The {$field} field is invalid";

        foreach ($params as $key => $value) {
            $message = str_replace("{{$key}}", (string)$value, $message);
        }

        return str_replace('{field}', $field, $message);
    }

    /**
     * Initialize default error messages
     */
    private function initializeDefaultMessages(): void
    {
        $this->messages = [
            'required' => 'The {field} field is required',
            'min' => 'The {field} field must be at least {min} characters',
            'max' => 'The {field} field must not exceed {max} characters',
            'email' => 'The {field} field must be a valid email address',
            'numeric' => 'The {field} field must be numeric',
            'integer' => 'The {field} field must be an integer',
            'url' => 'The {field} field must be a valid URL',
            'regex' => 'The {field} field format is invalid',
            'in' => 'The {field} field must be one of: {values}'
        ];
    }

    /**
     * Check for SQL injection patterns
     */
    private function containsSqlInjection(string $value): bool
    {
        $sqlPatterns = [
            '/(\bUNION\b.*\bSELECT\b)/i',
            '/(\bDROP\b.*\bTABLE\b)/i',
            '/(\bINSERT\b.*\bINTO\b)/i',
            '/(\bUPDATE\b.*\bSET\b)/i',
            '/(\bDELETE\b.*\bFROM\b)/i',
            '/(\bCREATE\b.*\bTABLE\b)/i',
            '/(\bALTER\b.*\bTABLE\b)/i',
            '/(\bEXEC\b.*\\()/i',
            '/(\bEXECUTE\b.*\\()/i',
            '/(--[^\r\n]*)/i',
            '/(\/\*.*?\*\/)/i',
            '/(\bxp_\w+)/i',
            '/(\bsp_\w+)/i',
            '/(\bWAITFOR\b.*\bDELAY\b)/i',
            '/(\bBENCHMARK\b.*\\()/i',
            '/(\bSLEEP\b.*\\()/i',
        ];

        foreach ($sqlPatterns as $pattern) {
            if (preg_match($pattern, $value)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check for XSS patterns
     */
    private function containsXss(string $value): bool
    {
        $xssPatterns = [
            '/<script[^>]*>.*?<\/script>/is',
            '/<iframe[^>]*>.*?<\/iframe>/is',
            '/<object[^>]*>.*?<\/object>/is',
            '/<embed[^>]*>/i',
            '/<applet[^>]*>.*?<\/applet>/is',
            '/<meta[^>]*>/i',
            '/<link[^>]*>/i',
            '/<style[^>]*>.*?<\/style>/is',
            '/javascript:/i',
            '/vbscript:/i',
            '/data:text\/html/i',
            '/on\w+\s*=/i', // Event handlers like onclick, onload, etc.
            '/<\s*\w+[^>]*\s+on\w+\s*=/i',
        ];

        foreach ($xssPatterns as $pattern) {
            if (preg_match($pattern, $value)) {
                return true;
            }
        }

        return false;
    }
}
