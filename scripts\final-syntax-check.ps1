# Final Accurate Syntax Check Script
Write-Host "=== WeBot Final Accurate Syntax Check ===" -ForegroundColor Green
Write-Host "Running comprehensive syntax validation..." -ForegroundColor Yellow
Write-Host ""

# Function to accurately check PHP syntax
function Check-PHPSyntaxAccurate {
    param([string]$filePath)
    
    $content = Get-Content $filePath -Raw -ErrorAction SilentlyContinue
    if (-not $content) {
        return @{ Valid = $false; Error = "Could not read file" }
    }
    
    # Remove strings and comments more accurately
    $cleanContent = $content
    
    # Remove single-line comments
    $cleanContent = $cleanContent -replace '//.*$', ''
    
    # Remove multi-line comments
    $cleanContent = $cleanContent -replace '/\*[\s\S]*?\*/', ''
    
    # Remove strings (both single and double quoted)
    $cleanContent = $cleanContent -replace '"([^"\\]|\\.)*"', '""'
    $cleanContent = $cleanContent -replace "'([^'\\]|\\.)*'", "''"
    
    # Count brackets in cleaned content
    $openParens = ($cleanContent.ToCharArray() | Where-Object { $_ -eq '(' }).Count
    $closeParens = ($cleanContent.ToCharArray() | Where-Object { $_ -eq ')' }).Count
    $openBraces = ($cleanContent.ToCharArray() | Where-Object { $_ -eq '{' }).Count
    $closeBraces = ($cleanContent.ToCharArray() | Where-Object { $_ -eq '}' }).Count
    $openSquare = ($cleanContent.ToCharArray() | Where-Object { $_ -eq '[' }).Count
    $closeSquare = ($cleanContent.ToCharArray() | Where-Object { $_ -eq ']' }).Count
    
    # Check for basic PHP structure
    $hasPhpTag = $content.StartsWith("<?php")
    $classMatches = [regex]::Matches($content, "class\s+\w+")
    $functionMatches = [regex]::Matches($content, "function\s+\w+")
    
    $issues = @()
    if ($openParens -ne $closeParens) {
        $issues += "Mismatched parentheses: $openParens open, $closeParens close"
    }
    if ($openBraces -ne $closeBraces) {
        $issues += "Mismatched braces: $openBraces open, $closeBraces close"
    }
    if ($openSquare -ne $closeSquare) {
        $issues += "Mismatched square brackets: $openSquare open, $closeSquare close"
    }
    if (-not $hasPhpTag) {
        $issues += "Missing PHP opening tag"
    }
    
    return @{
        Valid = ($issues.Count -eq 0)
        Issues = $issues
        Classes = $classMatches.Count
        Functions = $functionMatches.Count
        Lines = ($content -split "`n").Count
        HasPhpTag = $hasPhpTag
        OpenParens = $openParens
        CloseParens = $closeParens
        OpenBraces = $openBraces
        CloseBraces = $closeBraces
        OpenSquare = $openSquare
        CloseSquare = $closeSquare
    }
}

# Test all PHP files
Write-Host "1. Checking Source Files:" -ForegroundColor Cyan

$allValid = $true
$totalFiles = 0
$validFiles = 0
$totalIssues = 0

if (Test-Path "src") {
    $phpFiles = Get-ChildItem -Path "src" -Recurse -Filter "*.php"
    $totalFiles = $phpFiles.Count
    
    foreach ($file in $phpFiles) {
        $result = Check-PHPSyntaxAccurate $file.FullName
        
        if ($result.Valid) {
            Write-Host "  ✅ $($file.Name) ($($result.Lines) lines, $($result.Classes) classes, $($result.Functions) functions)" -ForegroundColor Green
            $validFiles++
        } else {
            Write-Host "  ❌ $($file.Name)" -ForegroundColor Red
            foreach ($issue in $result.Issues) {
                Write-Host "    - $issue" -ForegroundColor Red
                $totalIssues++
            }
            $allValid = $false
        }
    }
    
    $successRate = [math]::Round(($validFiles / $totalFiles) * 100, 1)
    Write-Host "  Summary: $validFiles/$totalFiles files valid ($successRate%)" -ForegroundColor $(if ($successRate -eq 100) { "Green" } elseif ($successRate -gt 90) { "Yellow" } else { "Red" })
} else {
    Write-Host "  ❌ src/ directory not found" -ForegroundColor Red
    $allValid = $false
}

Write-Host ""

# Test files
Write-Host "2. Checking Test Files:" -ForegroundColor Cyan

if (Test-Path "tests") {
    $testFiles = Get-ChildItem -Path "tests" -Recurse -Filter "*.php"
    $totalTestFiles = $testFiles.Count
    $validTestFiles = 0
    
    foreach ($file in $testFiles) {
        $result = Check-PHPSyntaxAccurate $file.FullName
        
        if ($result.Valid) {
            Write-Host "  ✅ $($file.Name) ($($result.Lines) lines, $($result.Functions) functions)" -ForegroundColor Green
            $validTestFiles++
        } else {
            Write-Host "  ❌ $($file.Name)" -ForegroundColor Red
            foreach ($issue in $result.Issues) {
                Write-Host "    - $issue" -ForegroundColor Red
            }
            $allValid = $false
        }
    }
    
    $testSuccessRate = [math]::Round(($validTestFiles / $totalTestFiles) * 100, 1)
    Write-Host "  Summary: $validTestFiles/$totalTestFiles test files valid ($testSuccessRate%)" -ForegroundColor $(if ($testSuccessRate -eq 100) { "Green" } elseif ($testSuccessRate -gt 90) { "Yellow" } else { "Red" })
} else {
    Write-Host "  ❌ tests/ directory not found" -ForegroundColor Red
}

Write-Host ""

# Check public files
Write-Host "3. Checking Public Files:" -ForegroundColor Cyan

if (Test-Path "public") {
    $publicFiles = Get-ChildItem -Path "public" -Filter "*.php"
    
    foreach ($file in $publicFiles) {
        $result = Check-PHPSyntaxAccurate $file.FullName
        
        if ($result.Valid) {
            Write-Host "  ✅ $($file.Name) ($($result.Lines) lines)" -ForegroundColor Green
        } else {
            Write-Host "  ❌ $($file.Name)" -ForegroundColor Red
            foreach ($issue in $result.Issues) {
                Write-Host "    - $issue" -ForegroundColor Red
            }
            $allValid = $false
        }
    }
} else {
    Write-Host "  ❌ public/ directory not found" -ForegroundColor Red
}

Write-Host ""

# Check dependencies
Write-Host "4. Checking Dependencies:" -ForegroundColor Cyan

if (Test-Path "vendor/autoload.php") {
    Write-Host "  ✅ Composer dependencies installed" -ForegroundColor Green
} else {
    Write-Host "  ❌ Missing vendor/autoload.php - run 'composer install'" -ForegroundColor Red
    $allValid = $false
}

if (Test-Path "composer.json") {
    try {
        $composer = Get-Content "composer.json" -Raw | ConvertFrom-Json
        Write-Host "  ✅ composer.json is valid" -ForegroundColor Green
        
        if ($composer.require) {
            $depCount = ($composer.require | Get-Member -MemberType NoteProperty).Count
            Write-Host "  Dependencies: $depCount" -ForegroundColor Gray
        }
    } catch {
        Write-Host "  composer.json is invalid" -ForegroundColor Red
        $allValid = $false
    }
} else {
    Write-Host "  composer.json not found" -ForegroundColor Red
    $allValid = $false
}

Write-Host ""

# Final summary
Write-Host "=== Final Summary ===" -ForegroundColor Green

if ($allValid) {
    Write-Host "ALL SYNTAX CHECKS PASSED!" -ForegroundColor Green
    Write-Host "Project is ready for PHP execution" -ForegroundColor Green
} else {
    Write-Host "Some issues found" -ForegroundColor Yellow
    Write-Host "Total issues: $totalIssues" -ForegroundColor Red
}

Write-Host ""
Write-Host "Project Statistics:" -ForegroundColor Cyan
Write-Host "  Source files: $validFiles/$totalFiles valid" -ForegroundColor White
Write-Host "  Test files: $validTestFiles/$totalTestFiles valid" -ForegroundColor White

Write-Host ""
Write-Host "Next steps:" -ForegroundColor Cyan
if ($allValid) {
    Write-Host "1. Install PHP 8.2+ if not available" -ForegroundColor White
    Write-Host "2. Run: composer install (if vendor/ missing)" -ForegroundColor White
    Write-Host "3. Run: php test-runner.php" -ForegroundColor White
    Write-Host "4. Run: vendor/bin/phpunit" -ForegroundColor White
} else {
    Write-Host "1. Fix syntax issues listed above" -ForegroundColor White
    Write-Host "2. Re-run this script to verify fixes" -ForegroundColor White
    Write-Host "3. Install PHP and dependencies" -ForegroundColor White
}

Write-Host ""
Write-Host "Final syntax check completed at: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor Gray
Write-Host "=========================" -ForegroundColor Green
