<?php

declare(strict_types=1);

/**
 * WeBot Mock Test Script
 * 
 * Tests if mock objects work correctly without PHPUnit.
 * 
 * @package WeBot\Scripts
 * @version 2.0
 */

// Load stubs
require_once __DIR__ . '/../tests/stubs/phpunit.php';

// Colors for output
const COLOR_RED = "\033[31m";
const COLOR_GREEN = "\033[32m";
const COLOR_YELLOW = "\033[33m";
const COLOR_BLUE = "\033[34m";
const COLOR_RESET = "\033[0m";

/**
 * Print colored message
 */
function printMessage(string $message, string $color = COLOR_RESET): void
{
    echo $color . $message . COLOR_RESET . PHP_EOL;
}

/**
 * Test mock functionality
 */
function testMockFunctionality(): int
{
    printMessage("🧪 WeBot Mock Test", COLOR_BLUE);
    printMessage("==================", COLOR_BLUE);
    printMessage("");
    
    $tests = [];
    
    // Test 1: Create MockObject
    try {
        $mock = new PHPUnit\Framework\MockObject\MockObject();
        $tests['MockObject Creation'] = 'OK';
        printMessage("  ✅ MockObject creation", COLOR_GREEN);
    } catch (Throwable $e) {
        $tests['MockObject Creation'] = 'Error: ' . $e->getMessage();
        printMessage("  ❌ MockObject creation - " . $e->getMessage(), COLOR_RED);
    }
    
    // Test 2: Mock methods
    try {
        $mock = new PHPUnit\Framework\MockObject\MockObject();
        $result = $mock->expects($mock->once())->method('test')->willReturn('value');
        $tests['Mock Methods'] = 'OK';
        printMessage("  ✅ Mock method chaining", COLOR_GREEN);
    } catch (Throwable $e) {
        $tests['Mock Methods'] = 'Error: ' . $e->getMessage();
        printMessage("  ❌ Mock method chaining - " . $e->getMessage(), COLOR_RED);
    }
    
    // Test 3: Invocation objects
    try {
        $invocation = new PHPUnit\Framework\MockObject\Invocation();
        $tests['Invocation Objects'] = 'OK';
        printMessage("  ✅ Invocation object creation", COLOR_GREEN);
    } catch (Throwable $e) {
        $tests['Invocation Objects'] = 'Error: ' . $e->getMessage();
        printMessage("  ❌ Invocation object creation - " . $e->getMessage(), COLOR_RED);
    }
    
    // Test 4: Constraint objects
    try {
        $constraint = new PHPUnit\Framework\MockObject\Constraint();
        $tests['Constraint Objects'] = 'OK';
        printMessage("  ✅ Constraint object creation", COLOR_GREEN);
    } catch (Throwable $e) {
        $tests['Constraint Objects'] = 'Error: ' . $e->getMessage();
        printMessage("  ❌ Constraint object creation - " . $e->getMessage(), COLOR_RED);
    }
    
    printMessage("");
    printMessage("📊 Summary", COLOR_BLUE);
    printMessage("----------", COLOR_BLUE);
    
    $successCount = count(array_filter($tests, fn($result) => $result === 'OK'));
    $totalCount = count($tests);
    
    printMessage("Total tests: $totalCount");
    printMessage("Successful: $successCount");
    printMessage("Failed: " . ($totalCount - $successCount));
    printMessage("Success rate: " . round(($successCount / $totalCount) * 100, 2) . "%");
    
    if ($successCount === $totalCount) {
        printMessage("🎉 All mock tests passed!", COLOR_GREEN);
        return 0;
    } else {
        printMessage("💥 Some mock tests failed", COLOR_RED);
        foreach ($tests as $test => $result) {
            if ($result !== 'OK') {
                printMessage("  - $test: $result", COLOR_RED);
            }
        }
        return 1;
    }
}

/**
 * Test class loading with stubs
 */
function testClassLoading(): int
{
    printMessage("📚 Testing Class Loading with Stubs", COLOR_BLUE);
    printMessage("===================================", COLOR_BLUE);
    printMessage("");
    
    $classes = [
        'PHPUnit\\Framework\\TestCase',
        'PHPUnit\\Framework\\MockObject\\MockObject',
        'PHPUnit\\Framework\\MockObject\\Invocation',
        'PHPUnit\\Framework\\MockObject\\Constraint'
    ];
    
    $loadedClasses = 0;
    $totalClasses = count($classes);
    
    foreach ($classes as $className) {
        if (class_exists($className)) {
            printMessage("  ✅ $className", COLOR_GREEN);
            $loadedClasses++;
        } else {
            printMessage("  ❌ $className", COLOR_RED);
        }
    }
    
    printMessage("");
    printMessage("Classes loaded: $loadedClasses/$totalClasses");
    
    return $loadedClasses === $totalClasses ? 0 : 1;
}

/**
 * Main execution
 */
function main(): int
{
    $result1 = testClassLoading();
    printMessage("");
    $result2 = testMockFunctionality();
    
    return max($result1, $result2);
}

// Run the test
exit(main());
