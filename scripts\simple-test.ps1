# WeBot Simple Testing Script
Write-Host "=== WeBot Simple Testing Script ===" -ForegroundColor Green
Write-Host "Starting basic project tests..." -ForegroundColor Yellow
Write-Host ""

# Test 1: Check project structure
Write-Host "1. Checking project structure:" -ForegroundColor Cyan

$requiredFiles = @(
    "composer.json",
    "src",
    "tests", 
    "config",
    "public",
    "migrations"
)

$missingFiles = @()
foreach ($file in $requiredFiles) {
    if (Test-Path $file) {
        Write-Host "✅ $file" -ForegroundColor Green
    } else {
        Write-Host "❌ $file (missing)" -ForegroundColor Red
        $missingFiles += $file
    }
}

if ($missingFiles.Count -eq 0) {
    Write-Host "✅ Project structure is complete" -ForegroundColor Green
} else {
    Write-Host "❌ Missing files: $($missingFiles -join ', ')" -ForegroundColor Red
}
Write-Host ""

# Test 2: Check composer.json
Write-Host "2. Checking composer.json:" -ForegroundColor Cyan

if (Test-Path "composer.json") {
    try {
        $composerContent = Get-Content "composer.json" -Raw | ConvertFrom-Json
        
        if ($composerContent.name) {
            Write-Host "✅ Project name: $($composerContent.name)" -ForegroundColor Green
        }
        
        if ($composerContent.require.php) {
            Write-Host "✅ PHP requirement: $($composerContent.require.php)" -ForegroundColor Green
        }
        
        $depCount = ($composerContent.require | Get-Member -MemberType NoteProperty).Count
        Write-Host "✅ Dependencies count: $depCount" -ForegroundColor Green
        
        if ($composerContent.autoload) {
            Write-Host "✅ Autoload configured" -ForegroundColor Green
        }
        
    } catch {
        Write-Host "❌ Error reading composer.json: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "❌ composer.json is missing" -ForegroundColor Red
}
Write-Host ""

# Test 3: Check configuration files
Write-Host "3. Checking configuration files:" -ForegroundColor Cyan

if (Test-Path ".env.example") {
    Write-Host "✅ .env.example exists" -ForegroundColor Green
} else {
    Write-Host "❌ .env.example missing" -ForegroundColor Red
}

if (Test-Path ".env") {
    Write-Host "✅ .env exists" -ForegroundColor Green
} else {
    Write-Host "⚠️ .env missing (should copy from .env.example)" -ForegroundColor Yellow
}

if (Test-Path ".env.testing") {
    Write-Host "✅ .env.testing exists" -ForegroundColor Green
} else {
    Write-Host "⚠️ .env.testing missing" -ForegroundColor Yellow
}
Write-Host ""

# Test 4: Check key files
Write-Host "4. Checking key files:" -ForegroundColor Cyan

$keyFiles = @(
    "public/index.php",
    "public/webhook.php", 
    "src/Core/TelegramBot.php",
    "src/Core/Database.php",
    "src/Core/Config.php",
    "tests/Unit/BaseTestCase.php",
    "phpunit.xml"
)

foreach ($file in $keyFiles) {
    if (Test-Path $file) {
        $size = (Get-Item $file).Length
        Write-Host "✅ $file ($size bytes)" -ForegroundColor Green
    } else {
        Write-Host "❌ $file (missing)" -ForegroundColor Red
    }
}
Write-Host ""

# Test 5: Check migrations
Write-Host "5. Checking migration files:" -ForegroundColor Cyan

if (Test-Path "migrations") {
    $migrationFiles = Get-ChildItem "migrations" -Filter "*.sql" | Sort-Object Name
    if ($migrationFiles.Count -gt 0) {
        Write-Host "✅ Migration files count: $($migrationFiles.Count)" -ForegroundColor Green
        foreach ($migration in $migrationFiles) {
            Write-Host "  - $($migration.Name)" -ForegroundColor Gray
        }
    } else {
        Write-Host "⚠️ No migration files found" -ForegroundColor Yellow
    }
} else {
    Write-Host "❌ migrations folder missing" -ForegroundColor Red
}
Write-Host ""

# Test 6: Check test files
Write-Host "6. Checking test files:" -ForegroundColor Cyan

if (Test-Path "tests") {
    $testFiles = Get-ChildItem "tests" -Recurse -Filter "*Test.php"
    if ($testFiles.Count -gt 0) {
        Write-Host "✅ Test files count: $($testFiles.Count)" -ForegroundColor Green
        
        $unitTests = $testFiles | Where-Object { $_.FullName -like "*Unit*" }
        $integrationTests = $testFiles | Where-Object { $_.FullName -like "*Integration*" }
        
        Write-Host "  - Unit Tests: $($unitTests.Count)" -ForegroundColor Gray
        Write-Host "  - Integration Tests: $($integrationTests.Count)" -ForegroundColor Gray
    } else {
        Write-Host "⚠️ No test files found" -ForegroundColor Yellow
    }
} else {
    Write-Host "❌ tests folder missing" -ForegroundColor Red
}
Write-Host ""

# Test 7: Check documentation
Write-Host "7. Checking documentation:" -ForegroundColor Cyan

$docFiles = @(
    "README.md",
    "docs/TESTING_AND_DEBUGGING_GUIDE.md",
    "docs/API_DOCUMENTATION.md"
)

foreach ($doc in $docFiles) {
    if (Test-Path $doc) {
        Write-Host "✅ $doc" -ForegroundColor Green
    } else {
        Write-Host "❌ $doc (missing)" -ForegroundColor Red
    }
}
Write-Host ""

# Summary
Write-Host "=== Summary ===" -ForegroundColor Green
Write-Host "Basic WeBot project tests completed." -ForegroundColor Yellow
Write-Host ""
Write-Host "Next steps for complete testing:" -ForegroundColor Cyan
Write-Host "1. Install PHP 8.2+" -ForegroundColor White
Write-Host "2. Install Composer" -ForegroundColor White
Write-Host "3. Run 'composer install'" -ForegroundColor White
Write-Host "4. Configure .env file" -ForegroundColor White
Write-Host "5. Run 'vendor/bin/phpunit'" -ForegroundColor White
Write-Host ""
Write-Host "Test completed at: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor Gray
Write-Host "=========================" -ForegroundColor Green
