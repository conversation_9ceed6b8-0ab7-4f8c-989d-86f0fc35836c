# 🧪 راهنمای کامل «تست» و «دیباگ» پروژه WeBot 2.0

> این راهنما تمام مراحل لازم برای راه‌اندازی محیط تست، اجرای انواع تست‌ها، دیباگ کد و پایش عملکرد را پوشش می‌دهد. از توسعهٔ محلی (Local) تا CI/CD، Docker و ابزارهای پایش در تولید.

---

## 1) پیش‌نیازها

| ابزار | نسخهٔ پیشنهادی | توضیحات |
|-------|----------------|---------|
| PHP | 8.1 یا بالاتر | ‌با Xdebug فعال برای دیباگ |
| Composer | 2.x | مدیریت وابستگی‌های PHP |
| Docker / Docker-Compose | جدیدترین | اجرای سرویس‌ها در کانتینر |
| MySQL | 8.0+ | دیتابیس اصلی |
| Redis | 6.x | کش و صف |
| NodeJS (اختیاری) | 18 LTS | اگر اسکریپت JS یا تولچین نیاز شد |
| Git | 2.40+ | کنترل نسخه |
| IDE با Xdebug | PhpStorm / VS Code | دیباگ خطی |

> **Tip:** اگر از Docker استفاده می‌کنید، تنها Docker+Compose کافی است و بقیهٔ سرویس‌ها داخل کانتینر بالا می‌آیند.

---

## 2) راه‌اندازی سریع با Docker

```bash
# 1. ساخت و اجرای کانتینرها
docker compose up -d  # nginx, php-fpm, mysql, redis

# 2. مانیتور لاگ‌ها (اختیاری)
docker compose logs -f php  # یا nginx / mysql / redis

# 3. اجرای مایگریشن و seeder
docker compose exec php php scripts/run-migrations.php --seed
```

### 2-1) تنظیم Xdebug در Docker

* فایل `docker/php/php.ini` از پیش Xdebug را فعال کرده است.
* در IDE «PHP Remote Interpreter» یا «Listen for Xdebug» را روشن کنید.
* پورت پیش‌فرض 9003 map شده است؛ در صورت نیاز به تغییر، `docker-compose.yml` را ویرایش کنید.

---

## 3) ساخت محیط محلی (بدون Docker)

```bash
# نصب وابستگی‌های PHP
composer install

# کپی env برای توسعه
cp .env.example .env

# تنظیم مقادیر دیتابیس و Redis در .env
nano .env

# اجرای مایگریشن و Seeder
php scripts/run-migrations.php --seed

# (اختیاری) نصب ابزارهای JS
npm ci --workspaces
```

> **هشدار:** از دیتابیس production استفاده نکنید؛ یک دیتابیس مستقل (webot_dev) بسازید.

---

## 4) اجرای تست‌ها

### 4-1) ساختار تست‌ها

```
/tests
├── Unit/               # تست واحد (کمترین وابستگی)
├── Integration/        # تست یکپارچه (DB، Redis …)
├── Feature/            # گردش‌های کاری کاربر
├── E2E/                # تست سرتاسری (API, Telegram, …)
└── Performance/        # تست کارایی و بار
```

### 4-2) اسکریپت‌های مفید

| دستور | توضیح |
|-------|-------|
| `composer test` | اجرای lint + phpstan + phpunit (اسکریپت تعریف شده در composer.json) |
| `vendor/bin/phpunit` | اجرای همهٔ تست‌ها |
| `vendor/bin/phpunit --testsuite unit` | فقط تست‌های واحد |
| `vendor/bin/phpunit --filter PaymentServiceTest` | یک کلاس / متد خاص |
| `vendor/bin/phpunit --coverage-html coverage/` | تولید گزارش پوشش کد |

### 4-3) تست در Docker

```bash
# اجرای تست توی کانتینر
docker compose exec php vendor/bin/phpunit
```

### 4-4) تست‌های عملکرد (Performance)

```bash
# تست کارایی دیتابیس و کش
docker compose exec php php tests/Performance/DatabasePerformanceTest.php

# CLI یکپارچه
php scripts/performance-optimizer.php analyze
```

---

## 5) دیباگ کد

### 5-1) Xdebug

1. در IDE حالت «Listen for Xdebug» را فعال کنید.
2. Breakpoint بگذارید (مثلاً در `PaymentService::processPayment`).
3. درخواست را اجرا کنید:
   ```bash
   docker compose exec php php index.php simulate/process-payment 123
   ```
4. IDE متوقف می‌شود و می‌توانید متغیرها را بررسی کنید.

> **نکتهٔ مهم:** حتماً مقدار `XDEBUG_MODE=debug,develop` در env یا php.ini تنظیم باشد.

### 5-2) دیباگ تست‌ها

```bash
# اجرای PHPUnit با Xdebug در VS Code
./vendor/bin/phpunit --filter UserControllerTest
```

### 5-3) لاگ‌ها و مانیتورینگ

| فایل / کانال | توضیح |
|--------------|-------|
| `storage/logs/webot.log` | لاگ اصلی اپلیکیشن |
| `storage/logs/error.log` | خطاهای PHP و Exception‌ها |
| Docker: `php` | لاگ php-fpm از کانتینر |
| Docker: `nginx` | لاگ دسترسی و خطای وب‌سرور |
| `src/Core/PerformanceMonitor.php` | متریک‌های اجرای توابع |

---

## 6) Continuous Integration (CI)

پوشهٔ `.github/workflows/ci.yml` شامل مراحل زیر است:

1. **checkout** سورس
2. نصب PHP با اکستنشن‌ها
3. `composer install`
4. اجرای `phpstan`
5. اجرای `phpunit` + گزارش پوشش کد
6. (اختیاری) اجرا‌ی Docker Compose برای تست integration

> تغییرات جدید خود را با Pull Request  بفرستید تا CI اجرا و نتیجه در GitHub نمایش داده شود.

---

## 7) عیب‌یابی رایج

| مشکل | علت محتمل | راه‌حل |
|-------|-----------|---------|
| «Class not found» | autoload به‌روز نیست | `composer dump-autoload` |
| «SQLSTATE[HY000] [2002] Connection refused» | سرویس DB بالا نیست یا env اشتباه است | `docker compose ps` و بررسی `.env` |
| Xdebug وصل نمی‌شود | پورت 9003 بسته یا IDE listening خاموش | بررسی تنظیمات IDE و `docker-compose.yml` |
| تست‌های Integration کندند | دیتابیس test مشترک با development | از `.env.testing` و DB مجزا استفاده کنید |
| Memory Limit Exceeded | تست در حال ایجاد اشیاء بزرگ | مقدار `memory_limit` را بالا ببرید یا داده را Mock کنید |

---

## 8) چشم‌انداز بهبود تست و دیباگ

1. **Mutation Testing** با ابزار `infection/infection` برای اطمینان بالاتر.
2. **Coverage Gate** در CI (شکست Pipeline اگر پوشش <80%).
3. **Tracing** درخواست‌ها با OpenTelemetry و ارسال به Jaeger.
4. **Integration Test Containers** (Testcontainers-PHP) برای Spin-up سرویس‌های موقتی.
5. **Load Testing** پیشرفته با k6 و اتصال به Grafana.

---

> با دنبال‌کردن این راهنما می‌توانید تغییرات را با اطمینان تست کرده، خطاها را سریع دیباگ کنید و عملکرد سیستم را زیر نظر بگیرید. موفق باشید! 🎉 