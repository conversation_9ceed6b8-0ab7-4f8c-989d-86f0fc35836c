# Test WeBot Without PHP Script
Write-Host "=== WeBot Test Without PHP ===" -ForegroundColor Green
Write-Host "Testing project structure and configuration..." -ForegroundColor Yellow
Write-Host ""

# Test 1: Project Structure
Write-Host "1. Project Structure Check:" -ForegroundColor Cyan

$requiredDirs = @(
    "src/Core",
    "src/Services", 
    "src/Controllers",
    "src/Models",
    "src/Middleware",
    "tests",
    "public",
    "config",
    "migrations",
    "storage"
)

$missingDirs = @()
foreach ($dir in $requiredDirs) {
    if (Test-Path $dir) {
        Write-Host "  ✅ $dir" -ForegroundColor Green
    } else {
        Write-Host "  ❌ $dir (missing)" -ForegroundColor Red
        $missingDirs += $dir
    }
}

Write-Host ""

# Test 2: Configuration Files
Write-Host "2. Configuration Files:" -ForegroundColor Cyan

$configFiles = @(
    "composer.json",
    ".env.example",
    "phpunit.xml",
    "config/app.php",
    "config/database.php"
)

$missingConfigs = @()
foreach ($file in $configFiles) {
    if (Test-Path $file) {
        Write-Host "  ✅ $file" -ForegroundColor Green
    } else {
        Write-Host "  ❌ $file (missing)" -ForegroundColor Red
        $missingConfigs += $file
    }
}

Write-Host ""

# Test 3: Core Files Count
Write-Host "3. Core Files Analysis:" -ForegroundColor Cyan

if (Test-Path "src") {
    $phpFiles = Get-ChildItem -Path "src" -Recurse -Filter "*.php" -ErrorAction SilentlyContinue
    Write-Host "  📁 Total PHP files: $($phpFiles.Count)" -ForegroundColor White
    
    $coreFiles = Get-ChildItem -Path "src/Core" -Filter "*.php" -ErrorAction SilentlyContinue
    Write-Host "  🔧 Core files: $($coreFiles.Count)" -ForegroundColor White
    
    $serviceFiles = Get-ChildItem -Path "src/Services" -Filter "*.php" -ErrorAction SilentlyContinue
    Write-Host "  ⚙️ Service files: $($serviceFiles.Count)" -ForegroundColor White
    
    $controllerFiles = Get-ChildItem -Path "src/Controllers" -Filter "*.php" -ErrorAction SilentlyContinue
    Write-Host "  🎮 Controller files: $($controllerFiles.Count)" -ForegroundColor White
}

Write-Host ""

# Test 4: Test Files
Write-Host "4. Test Files Analysis:" -ForegroundColor Cyan

if (Test-Path "tests") {
    $testFiles = Get-ChildItem -Path "tests" -Recurse -Filter "*.php" -ErrorAction SilentlyContinue
    Write-Host "  🧪 Total test files: $($testFiles.Count)" -ForegroundColor White
    
    $unitTests = $testFiles | Where-Object { $_.FullName -like "*Unit*" }
    Write-Host "  🔬 Unit tests: $($unitTests.Count)" -ForegroundColor White
    
    $integrationTests = $testFiles | Where-Object { $_.FullName -like "*Integration*" }
    Write-Host "  🔗 Integration tests: $($integrationTests.Count)" -ForegroundColor White
}

Write-Host ""

# Test 5: Dependencies Check
Write-Host "5. Dependencies Status:" -ForegroundColor Cyan

if (Test-Path "composer.json") {
    try {
        $composer = Get-Content "composer.json" -Raw | ConvertFrom-Json
        Write-Host "  composer.json is valid" -ForegroundColor Green

        if ($composer.require) {
            $depCount = ($composer.require | Get-Member -MemberType NoteProperty).Count
            Write-Host "  Required dependencies: $depCount" -ForegroundColor White

            # List main dependencies
            $mainDeps = @()
            if ($composer.require.'guzzlehttp/guzzle') { $mainDeps += "Guzzle HTTP" }
            if ($composer.require.'firebase/php-jwt') { $mainDeps += "JWT" }
            if ($composer.require.'phpunit/phpunit') { $mainDeps += "PHPUnit" }
            if ($composer.require.'monolog/monolog') { $mainDeps += "Monolog" }

            if ($mainDeps.Count -gt 0) {
                Write-Host "  Key dependencies: $($mainDeps -join ', ')" -ForegroundColor Gray
            }
        }
    } catch {
        Write-Host "  composer.json is invalid" -ForegroundColor Red
    }
} else {
    Write-Host "  composer.json not found" -ForegroundColor Red
}

if (Test-Path "vendor/autoload.php") {
    Write-Host "  Dependencies installed" -ForegroundColor Green
} else {
    Write-Host "  Dependencies not installed - run 'composer install'" -ForegroundColor Red
}

Write-Host ""

# Test 6: Environment Setup
Write-Host "6. Environment Setup:" -ForegroundColor Cyan

if (Test-Path ".env") {
    Write-Host "  ✅ .env file exists" -ForegroundColor Green
} else {
    Write-Host "  ⚠️ .env file missing - copy from .env.example" -ForegroundColor Yellow
}

if (Test-Path ".env.example") {
    Write-Host "  ✅ .env.example exists" -ForegroundColor Green
    
    $envExample = Get-Content ".env.example" -ErrorAction SilentlyContinue
    $envVars = ($envExample | Where-Object { $_ -match "^[A-Z_]+=.*" }).Count
    Write-Host "  ⚙️ Environment variables: $envVars" -ForegroundColor Gray
} else {
    Write-Host "  ❌ .env.example missing" -ForegroundColor Red
}

Write-Host ""

# Test 7: Storage Directories
Write-Host "7. Storage Directories:" -ForegroundColor Cyan

$storageDirs = @("storage", "storage/logs", "storage/cache", "storage/sessions", "storage/uploads")
foreach ($dir in $storageDirs) {
    if (Test-Path $dir) {
        Write-Host "  ✅ $dir" -ForegroundColor Green
    } else {
        Write-Host "  ❌ $dir (missing)" -ForegroundColor Red
    }
}

Write-Host ""

# Summary
Write-Host "=== Summary ===" -ForegroundColor Green

$totalIssues = $missingDirs.Count + $missingConfigs.Count
if (!(Test-Path "vendor/autoload.php")) { $totalIssues++ }
if (!(Test-Path ".env")) { $totalIssues++ }

if ($totalIssues -eq 0) {
    Write-Host "🎉 Project structure is complete!" -ForegroundColor Green
    Write-Host "✅ Ready for PHP installation and testing" -ForegroundColor Green
} else {
    Write-Host "⚠️ Found $totalIssues issues" -ForegroundColor Yellow
    
    if ($missingDirs.Count -gt 0) {
        Write-Host "Missing directories: $($missingDirs -join ', ')" -ForegroundColor Red
    }
    if ($missingConfigs.Count -gt 0) {
        Write-Host "Missing config files: $($missingConfigs -join ', ')" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "Next Steps:" -ForegroundColor Cyan
Write-Host "1. Install PHP 8.2+ (see INSTALL_PHP_GUIDE.md)" -ForegroundColor White
Write-Host "2. Run: composer install" -ForegroundColor White
Write-Host "3. Copy .env.example to .env" -ForegroundColor White
Write-Host "4. Configure database settings in .env" -ForegroundColor White
Write-Host "5. Run: php test-runner.php" -ForegroundColor White
Write-Host "6. Run: vendor/bin/phpunit" -ForegroundColor White

Write-Host ""
Write-Host "Test completed at: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor Gray
Write-Host "=========================" -ForegroundColor Green
