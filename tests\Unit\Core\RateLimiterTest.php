<?php

declare(strict_types=1);

namespace WeBot\Tests\Unit\Core;

use WeBot\Tests\Unit\BaseTestCase;
use PHPUnit\Framework\MockObject\MockObject;
use WeBot\Core\RateLimiter;
use WeBot\Core\Cache\Contracts\CacheInterface;

/**
 * Rate Limiter Test
 * 
 * Unit tests for RateLimiter class.
 * 
 * @package WeBot\Tests\Unit\Core
 * @version 2.0
 */
class RateLimiterTest extends BaseTestCase
{
    private RateLimiter $rateLimiter;
    /** @var MockObject&CacheInterface */
    private MockObject $mockCache;

    protected function setUp(): void
    {
        parent::setUp();

        /** @var MockObject&CacheInterface $mockCache */
        $mockCache = $this->createMock(CacheInterface::class);
        $this->mockCache = $mockCache;
        
        $config = [
            'default_limit' => 10,
            'default_window' => 60,
            'burst_limit' => 5,
            'burst_window' => 1,
            'enabled' => true
        ];
        
        $this->rateLimiter = new RateLimiter($this->mockCache, $config);
    }

    /**
     * Test rate limiter allows requests within limit
     */
    public function testAllowsRequestsWithinLimit(): void
    {
        // Arrange
        $key = 'test_user_123';
        
        // Mock cache to return low count
        $this->mockCache
            ->expects($this->once())
            ->method('get')
            ->willReturn(5); // 5 requests in current window
            
        $this->mockCache
            ->expects($this->once())
            ->method('increment')
            ->willReturn(6);

        // Act
        $result = $this->rateLimiter->isAllowed($key);

        // Assert
        $this->assertTrue($result);
    }

    /**
     * Test rate limiter blocks requests over limit
     */
    public function testBlocksRequestsOverLimit(): void
    {
        // Arrange
        $key = 'test_user_123';
        
        // Mock cache to return high count
        $this->mockCache
            ->expects($this->once())
            ->method('get')
            ->willReturn(10); // Already at limit

        // Act
        $result = $this->rateLimiter->isAllowed($key);

        // Assert
        $this->assertFalse($result);
    }

    /**
     * Test rate limiter with custom options
     */
    public function testCustomOptions(): void
    {
        // Arrange
        $key = 'test_user_123';
        $options = [
            'limit' => 5,
            'window' => 30
        ];
        
        $this->mockCache
            ->expects($this->once())
            ->method('get')
            ->willReturn(3);
            
        $this->mockCache
            ->expects($this->once())
            ->method('increment')
            ->willReturn(4);

        // Act
        $result = $this->rateLimiter->isAllowed($key, $options);

        // Assert
        $this->assertTrue($result);
    }

    /**
     * Test rate limiter when disabled
     */
    public function testDisabledRateLimiter(): void
    {
        // Arrange
        $config = ['enabled' => false];
        /** @var CacheInterface $mockCache */
        $mockCache = $this->mockCache;
        $rateLimiter = new RateLimiter($mockCache, $config);
        
        // Cache should not be called when disabled
        $this->mockCache
            ->expects($this->never())
            ->method('get');

        // Act
        $result = $rateLimiter->isAllowed('any_key');

        // Assert
        $this->assertTrue($result);
    }

    /**
     * Test burst protection
     */
    public function testBurstProtection(): void
    {
        // Arrange
        $key = 'test_user_123';
        
        // Mock cache for burst window check
        $this->mockCache
            ->expects($this->exactly(2))
            ->method('get')
            ->willReturnOnConsecutiveCalls(
                3,  // Normal window count
                6   // Burst window count (over burst limit of 5)
            );

        // Act
        $result = $this->rateLimiter->isAllowed($key);

        // Assert
        $this->assertFalse($result);
    }

    /**
     * Test whitelist functionality
     */
    public function testWhitelist(): void
    {
        // Arrange
        $config = [
            'whitelist' => ['127.0.0.1', 'trusted_user_123'],
            'enabled' => true
        ];
        /** @var CacheInterface $mockCache */
        $mockCache = $this->mockCache;
        $rateLimiter = new RateLimiter($mockCache, $config);
        
        // Cache should not be called for whitelisted keys
        $this->mockCache
            ->expects($this->never())
            ->method('get');

        // Act
        $result = $rateLimiter->isAllowed('trusted_user_123');

        // Assert
        $this->assertTrue($result);
    }

    /**
     * Test blacklist functionality
     */
    public function testBlacklist(): void
    {
        // Arrange
        $config = [
            'blacklist' => ['banned_user_123', '192.168.1.100'],
            'enabled' => true
        ];
        /** @var CacheInterface $mockCache */
        $mockCache = $this->mockCache;
        $rateLimiter = new RateLimiter($mockCache, $config);

        // Act
        $result = $rateLimiter->isAllowed('banned_user_123');

        // Assert
        $this->assertFalse($result);
    }

    /**
     * Test getting remaining attempts
     */
    public function testGetRemainingAttempts(): void
    {
        // Arrange
        $key = 'test_user_123';
        
        $this->mockCache
            ->expects($this->once())
            ->method('get')
            ->willReturn(7); // 7 requests used

        // Act
        $remaining = $this->rateLimiter->getRemainingAttempts($key);

        // Assert
        $this->assertEquals(3, $remaining); // 10 - 7 = 3
    }

    /**
     * Test getting time until reset
     */
    public function testGetTimeUntilReset(): void
    {
        // Arrange
        $key = 'test_user_123';
        
        $this->mockCache
            ->expects($this->once())
            ->method('get')
            ->willReturn(time() + 30); // Reset in 30 seconds

        // Act
        $timeUntilReset = $this->rateLimiter->getTimeUntilReset($key);

        // Assert
        $this->assertGreaterThan(25, $timeUntilReset);
        $this->assertLessThanOrEqual(30, $timeUntilReset);
    }

    /**
     * Test reset functionality
     */
    public function testReset(): void
    {
        // Arrange
        $key = 'test_user_123';
        
        $this->mockCache
            ->expects($this->exactly(2))
            ->method('delete')
            ->willReturn(true);

        // Act
        $result = $this->rateLimiter->reset($key);

        // Assert
        $this->assertTrue($result);
    }

    /**
     * Test ban functionality
     */
    public function testBanUser(): void
    {
        // Arrange
        $key = 'test_user_123';
        $duration = 3600; // 1 hour
        
        $this->mockCache
            ->expects($this->once())
            ->method('set')
            ->with(
                $this->stringContains('ban:'),
                true,
                $duration
            )
            ->willReturn(true);

        // Act
        $result = $this->rateLimiter->banUser($key, $duration);

        // Assert
        $this->assertTrue($result);
    }

    /**
     * Test checking if user is banned
     */
    public function testIsBanned(): void
    {
        // Arrange
        $key = 'test_user_123';
        
        $this->mockCache
            ->expects($this->once())
            ->method('exists')
            ->with($this->stringContains('ban:'))
            ->willReturn(true);

        // Act
        $result = $this->rateLimiter->isBanned($key);

        // Assert
        $this->assertTrue($result);
    }

    protected function tearDown(): void
    {
        parent::tearDown();
        
        unset($this->rateLimiter, $this->mockCache);
    }
}
