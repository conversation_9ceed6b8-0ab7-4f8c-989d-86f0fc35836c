# Find Missing Parentheses Script
param([string]$FilePath = "src/Core/InputValidator.php")

Write-Host "=== Finding Missing Parentheses ===" -ForegroundColor Green
Write-Host "Analyzing file: $FilePath" -ForegroundColor Yellow
Write-Host ""

if (!(Test-Path $FilePath)) {
    Write-Host "File not found: $FilePath" -ForegroundColor Red
    exit 1
}

$content = Get-Content $FilePath
$lineNumber = 0
$openParens = 0
$closeParens = 0
$issues = @()

foreach ($line in $content) {
    $lineNumber++
    
    # Count parentheses in this line
    $lineOpenParens = ($line.ToCharArray() | Where-Object { $_ -eq '(' }).Count
    $lineCloseParens = ($line.ToCharArray() | Where-Object { $_ -eq ')' }).Count
    
    $openParens += $lineOpenParens
    $closeParens += $lineCloseParens
    
    # Check for potential issues
    if ($lineOpenParens -gt 0 -or $lineCloseParens -gt 0) {
        $balance = $openParens - $closeParens
        
        if ($lineOpenParens -ne $lineCloseParens) {
            $issues += @{
                Line = $lineNumber
                Content = $line.Trim()
                OpenParens = $lineOpenParens
                CloseParens = $lineCloseParens
                RunningBalance = $balance
            }
        }
    }
}

Write-Host "Analysis Results:" -ForegroundColor Cyan
Write-Host "Total open parentheses: $openParens" -ForegroundColor White
Write-Host "Total close parentheses: $closeParens" -ForegroundColor White
Write-Host "Difference: $($openParens - $closeParens)" -ForegroundColor $(if ($openParens -eq $closeParens) { "Green" } else { "Red" })
Write-Host ""

if ($issues.Count -gt 0) {
    Write-Host "Lines with unbalanced parentheses:" -ForegroundColor Yellow
    
    foreach ($issue in $issues) {
        $color = if ($issue.RunningBalance -eq 0) { "Green" } elseif ($issue.RunningBalance -gt 0) { "Yellow" } else { "Red" }
        Write-Host "Line $($issue.Line): ($($issue.OpenParens) open, $($issue.CloseParens) close, balance: $($issue.RunningBalance))" -ForegroundColor $color
        Write-Host "  $($issue.Content)" -ForegroundColor Gray
        Write-Host ""
    }
}

# Look for specific problematic patterns
Write-Host "Checking for common issues:" -ForegroundColor Cyan

$problematicLines = @()
$lineNumber = 0

foreach ($line in $content) {
    $lineNumber++
    
    # Check for regex patterns that might have issues
    if ($line -match 'preg_match\(' -and $line -match '/.*\(.*\).*/' -and !($line -match '\\\(')) {
        $problematicLines += @{
            Line = $lineNumber
            Content = $line.Trim()
            Issue = "Regex with unescaped parentheses"
        }
    }
    
    # Check for function calls with missing closing parens
    if ($line -match '\w+\([^)]*$' -and !($line -match '//')) {
        $problematicLines += @{
            Line = $lineNumber
            Content = $line.Trim()
            Issue = "Possible missing closing parenthesis"
        }
    }
}

if ($problematicLines.Count -gt 0) {
    Write-Host "Potentially problematic lines:" -ForegroundColor Yellow
    
    foreach ($problem in $problematicLines) {
        Write-Host "Line $($problem.Line): $($problem.Issue)" -ForegroundColor Red
        Write-Host "  $($problem.Content)" -ForegroundColor Gray
        Write-Host ""
    }
} else {
    Write-Host "No obvious problematic patterns found." -ForegroundColor Green
}

Write-Host "Analysis completed." -ForegroundColor Green
