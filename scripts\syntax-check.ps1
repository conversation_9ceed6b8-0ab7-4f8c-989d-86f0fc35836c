# WeBot PHP Syntax Check Script
Write-Host "=== WeBot PHP Syntax Check Script ===" -ForegroundColor Green
Write-Host "Checking PHP files for basic syntax issues..." -ForegroundColor Yellow
Write-Host ""

# Function to check PHP syntax without running PHP
function Check-PHPSyntax {
    param([string]$filePath)
    
    $content = Get-Content $filePath -Raw -ErrorAction SilentlyContinue
    if (-not $content) {
        return @{ Valid = $false; Error = "Could not read file" }
    }
    
    $issues = @()
    
    # Check for basic PHP syntax issues
    if (-not $content.StartsWith("<?php")) {
        $issues += "Missing PHP opening tag"
    }
    
    # Check for unclosed brackets
    $openBraces = ($content.ToCharArray() | Where-Object { $_ -eq '{' }).Count
    $closeBraces = ($content.ToCharArray() | Where-Object { $_ -eq '}' }).Count
    if ($openBraces -ne $closeBraces) {
        $issues += "Mismatched braces: $openBraces open, $closeBraces close"
    }
    
    # Check for unclosed parentheses
    $openParens = ($content.ToCharArray() | Where-Object { $_ -eq '(' }).Count
    $closeParens = ($content.ToCharArray() | Where-Object { $_ -eq ')' }).Count
    if ($openParens -ne $closeParens) {
        $issues += "Mismatched parentheses: $openParens open, $closeParens close"
    }
    
    # Check for unclosed square brackets
    $openSquare = ($content.ToCharArray() | Where-Object { $_ -eq '[' }).Count
    $closeSquare = ($content.ToCharArray() | Where-Object { $_ -eq ']' }).Count
    if ($openSquare -ne $closeSquare) {
        $issues += "Mismatched square brackets: $openSquare open, $closeSquare close"
    }
    
    # Check for basic class/function structure
    $classMatches = [regex]::Matches($content, "class\s+\w+")
    $functionMatches = [regex]::Matches($content, "function\s+\w+")
    
    return @{
        Valid = ($issues.Count -eq 0)
        Issues = $issues
        Classes = $classMatches.Count
        Functions = $functionMatches.Count
        Lines = ($content -split "`n").Count
    }
}

# Test 1: Check all PHP files in src/
Write-Host "1. Checking Source Files:" -ForegroundColor Cyan

if (Test-Path "src") {
    $phpFiles = Get-ChildItem -Path "src" -Recurse -Filter "*.php"
    $totalFiles = $phpFiles.Count
    $validFiles = 0
    $totalIssues = 0
    
    foreach ($file in $phpFiles) {
        $result = Check-PHPSyntax $file.FullName
        
        if ($result.Valid) {
            Write-Host "  ✅ $($file.Name) ($($result.Lines) lines, $($result.Classes) classes, $($result.Functions) functions)" -ForegroundColor Green
            $validFiles++
        } else {
            Write-Host "  ❌ $($file.Name)" -ForegroundColor Red
            foreach ($issue in $result.Issues) {
                Write-Host "    - $issue" -ForegroundColor Red
                $totalIssues++
            }
        }
    }
    
    $successRate = [math]::Round(($validFiles / $totalFiles) * 100, 1)
    Write-Host "  Summary: $validFiles/$totalFiles files valid ($successRate%)" -ForegroundColor $(if ($successRate -eq 100) { "Green" } elseif ($successRate -gt 80) { "Yellow" } else { "Red" })
    if ($totalIssues -gt 0) {
        Write-Host "  Total issues found: $totalIssues" -ForegroundColor Red
    }
} else {
    Write-Host "  ❌ src/ directory not found" -ForegroundColor Red
}
Write-Host ""

# Test 2: Check test files
Write-Host "2. Checking Test Files:" -ForegroundColor Cyan

if (Test-Path "tests") {
    $testFiles = Get-ChildItem -Path "tests" -Recurse -Filter "*.php"
    $totalTestFiles = $testFiles.Count
    $validTestFiles = 0
    $totalTestIssues = 0
    
    foreach ($file in $testFiles) {
        $result = Check-PHPSyntax $file.FullName
        
        if ($result.Valid) {
            Write-Host "  ✅ $($file.Name) ($($result.Lines) lines, $($result.Functions) functions)" -ForegroundColor Green
            $validTestFiles++
        } else {
            Write-Host "  ❌ $($file.Name)" -ForegroundColor Red
            foreach ($issue in $result.Issues) {
                Write-Host "    - $issue" -ForegroundColor Red
                $totalTestIssues++
            }
        }
    }
    
    $testSuccessRate = [math]::Round(($validTestFiles / $totalTestFiles) * 100, 1)
    Write-Host "  Summary: $validTestFiles/$totalTestFiles test files valid ($testSuccessRate%)" -ForegroundColor $(if ($testSuccessRate -eq 100) { "Green" } elseif ($testSuccessRate -gt 80) { "Yellow" } else { "Red" })
    if ($totalTestIssues -gt 0) {
        Write-Host "  Total test issues found: $totalTestIssues" -ForegroundColor Red
    }
} else {
    Write-Host "  ❌ tests/ directory not found" -ForegroundColor Red
}
Write-Host ""

# Test 3: Check public files
Write-Host "3. Checking Public Files:" -ForegroundColor Cyan

if (Test-Path "public") {
    $publicFiles = Get-ChildItem -Path "public" -Filter "*.php"
    
    foreach ($file in $publicFiles) {
        $result = Check-PHPSyntax $file.FullName
        
        if ($result.Valid) {
            Write-Host "  ✅ $($file.Name) ($($result.Lines) lines)" -ForegroundColor Green
        } else {
            Write-Host "  ❌ $($file.Name)" -ForegroundColor Red
            foreach ($issue in $result.Issues) {
                Write-Host "    - $issue" -ForegroundColor Red
            }
        }
    }
} else {
    Write-Host "  ❌ public/ directory not found" -ForegroundColor Red
}
Write-Host ""

# Test 4: Check configuration files
Write-Host "4. Checking Configuration Files:" -ForegroundColor Cyan

$configFiles = @()
if (Test-Path "config") {
    $configFiles = Get-ChildItem -Path "config" -Filter "*.php"
}

if ($configFiles.Count -gt 0) {
    foreach ($file in $configFiles) {
        $result = Check-PHPSyntax $file.FullName
        
        if ($result.Valid) {
            Write-Host "  ✅ $($file.Name) ($($result.Lines) lines)" -ForegroundColor Green
        } else {
            Write-Host "  ❌ $($file.Name)" -ForegroundColor Red
            foreach ($issue in $result.Issues) {
                Write-Host "    - $issue" -ForegroundColor Red
            }
        }
    }
} else {
    Write-Host "  ⚠️ No PHP config files found" -ForegroundColor Yellow
}
Write-Host ""

# Test 5: Check for common issues
Write-Host "5. Common Issues Check:" -ForegroundColor Cyan

$commonIssues = @()

# Check for missing autoloader
if (-not (Test-Path "vendor/autoload.php")) {
    $commonIssues += "Missing vendor/autoload.php - run 'composer install'"
}

# Check for .env file
if (-not (Test-Path ".env")) {
    $commonIssues += "Missing .env file - copy from .env.example"
}

# Check for storage directory
if (-not (Test-Path "storage")) {
    $commonIssues += "Missing storage/ directory"
} else {
    $storageSubdirs = @("logs", "cache", "sessions", "uploads")
    foreach ($subdir in $storageSubdirs) {
        if (-not (Test-Path "storage/$subdir")) {
            $commonIssues += "Missing storage/$subdir directory"
        }
    }
}

if ($commonIssues.Count -eq 0) {
    Write-Host "  ✅ No common issues found" -ForegroundColor Green
} else {
    foreach ($issue in $commonIssues) {
        Write-Host "  ❌ $issue" -ForegroundColor Red
    }
}
Write-Host ""

# Summary
Write-Host "=== Syntax Check Summary ===" -ForegroundColor Green

if (Test-Path "src") {
    Write-Host "Source files: $validFiles/$totalFiles valid" -ForegroundColor White
}
if (Test-Path "tests") {
    Write-Host "Test files: $validTestFiles/$totalTestFiles valid" -ForegroundColor White
}
Write-Host "Common issues: $($commonIssues.Count)" -ForegroundColor White

Write-Host ""
Write-Host "Next steps:" -ForegroundColor Cyan
Write-Host "1. Fix any syntax issues found above" -ForegroundColor White
Write-Host "2. Install PHP 8.2+ to run actual syntax check: php -l filename.php" -ForegroundColor White
Write-Host "3. Install dependencies: composer install" -ForegroundColor White
Write-Host "4. Run tests: vendor/bin/phpunit" -ForegroundColor White

Write-Host ""
Write-Host "Syntax check completed at: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor Gray
Write-Host "=========================" -ForegroundColor Green
