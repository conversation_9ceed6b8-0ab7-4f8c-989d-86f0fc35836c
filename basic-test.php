#!/usr/bin/env php
<?php
/**
 * Basic WeBot Test - No Database Required
 */

echo "=== WeBot Basic Test (No Database) ===\n";
echo "Testing core functionality...\n\n";

// Test 1: PHP Version
echo "1. PHP Environment:\n";
echo "   ✅ PHP Version: " . PHP_VERSION . "\n";
echo "   ✅ PHP SAPI: " . php_sapi_name() . "\n";

// Test 2: Required Extensions
echo "\n2. Required Extensions:\n";
$requiredExtensions = ['curl', 'json', 'mbstring', 'openssl', 'mysqli'];
$missingExtensions = [];

foreach ($requiredExtensions as $ext) {
    if (extension_loaded($ext)) {
        echo "   ✅ $ext\n";
    } else {
        echo "   ❌ $ext (missing)\n";
        $missingExtensions[] = $ext;
    }
}

// Test 3: File Structure
echo "\n3. Project Structure:\n";
$requiredFiles = [
    'vendor/autoload.php' => 'Composer autoloader',
    'composer.json' => 'Composer configuration',
    'src/Core/Config.php' => 'Core Config class',
    'src/Utils/Helper.php' => 'Helper utilities',
    'config/app.php' => 'App configuration'
];

$missingFiles = [];
foreach ($requiredFiles as $file => $description) {
    if (file_exists($file)) {
        echo "   ✅ $description ($file)\n";
    } else {
        echo "   ❌ $description ($file)\n";
        $missingFiles[] = $file;
    }
}

// Test 4: Composer Dependencies
echo "\n4. Composer Dependencies:\n";
if (file_exists('vendor/autoload.php')) {
    echo "   ✅ Vendor directory exists\n";
    
    // Check key packages
    $packages = [
        'vendor/guzzlehttp/guzzle' => 'Guzzle HTTP Client',
        'vendor/firebase/php-jwt' => 'Firebase JWT',
        'vendor/monolog/monolog' => 'Monolog Logger',
        'vendor/phpunit/phpunit' => 'PHPUnit Testing'
    ];
    
    foreach ($packages as $path => $name) {
        if (is_dir($path)) {
            echo "   ✅ $name\n";
        } else {
            echo "   ❌ $name (not installed)\n";
        }
    }
} else {
    echo "   ❌ Composer dependencies not installed\n";
}

// Test 5: Configuration Files
echo "\n5. Configuration Files:\n";
$configFiles = [
    '.env.example' => 'Environment template',
    'config/app.php' => 'Application config',
    'config/database.php' => 'Database config'
];

foreach ($configFiles as $file => $description) {
    if (file_exists($file)) {
        echo "   ✅ $description ($file)\n";
    } else {
        echo "   ❌ $description ($file)\n";
    }
}

// Test 6: Basic Class Loading (without database)
echo "\n6. Basic Class Loading:\n";
if (file_exists('vendor/autoload.php')) {
    try {
        // Load only composer autoloader, not our bootstrap
        require_once 'vendor/autoload.php';
        echo "   ✅ Composer autoloader loaded\n";
        
        // Test some basic classes that don't need database
        $testClasses = [
            'GuzzleHttp\\Client' => 'Guzzle HTTP Client',
            'Firebase\\JWT\\JWT' => 'Firebase JWT',
            'Monolog\\Logger' => 'Monolog Logger'
        ];
        
        foreach ($testClasses as $class => $name) {
            if (class_exists($class)) {
                echo "   ✅ $name ($class)\n";
            } else {
                echo "   ❌ $name ($class)\n";
            }
        }
        
    } catch (Exception $e) {
        echo "   ❌ Autoloader error: " . $e->getMessage() . "\n";
    }
}

// Test 7: Storage Directories
echo "\n7. Storage Directories:\n";
$storageDirs = [
    'storage/logs' => 'Log files',
    'storage/cache' => 'Cache files',
    'storage/sessions' => 'Session files',
    'storage/uploads' => 'Upload files'
];

foreach ($storageDirs as $dir => $description) {
    if (is_dir($dir)) {
        $writable = is_writable($dir) ? 'writable' : 'not writable';
        echo "   ✅ $description ($dir - $writable)\n";
    } else {
        echo "   ❌ $description ($dir)\n";
    }
}

// Summary
echo "\n=== Test Summary ===\n";

$totalIssues = count($missingExtensions) + count($missingFiles);

if ($totalIssues === 0) {
    echo "🎉 All basic tests passed!\n";
    echo "✅ PHP environment is ready\n";
    echo "✅ Dependencies are installed\n";
    echo "✅ Project structure is complete\n";
    
    echo "\nNext steps:\n";
    echo "1. Set up MySQL database\n";
    echo "2. Copy .env.example to .env and configure\n";
    echo "3. Run database migrations\n";
    echo "4. Run full test suite: vendor/bin/phpunit\n";
    
} else {
    echo "⚠️ Found $totalIssues issues:\n";
    
    if (!empty($missingExtensions)) {
        echo "Missing extensions: " . implode(', ', $missingExtensions) . "\n";
    }
    
    if (!empty($missingFiles)) {
        echo "Missing files: " . implode(', ', $missingFiles) . "\n";
    }
    
    echo "\nPlease fix these issues before proceeding.\n";
}

echo "\nBasic test completed at: " . date('Y-m-d H:i:s') . "\n";
echo "=========================\n";
