# WeBot PHP Installation Script
Write-Host "=== WeBot PHP Installation Script ===" -ForegroundColor Green
Write-Host "Installing PHP and dependencies..." -ForegroundColor Yellow
Write-Host ""

# Create php directory
$phpDir = "php-portable"
if (!(Test-Path $phpDir)) {
    New-Item -ItemType Directory -Path $phpDir -Force | Out-Null
}

# Download PHP
Write-Host "1. Downloading PHP 8.2..." -ForegroundColor Cyan
$phpUrl = "https://windows.php.net/downloads/releases/php-8.2.26-Win32-vs16-x64.zip"
$phpZip = "$phpDir/php.zip"

try {
    # Try alternative download method
    $webClient = New-Object System.Net.WebClient
    $webClient.DownloadFile("https://windows.php.net/downloads/releases/php-8.2.26-Win32-vs16-x64.zip", $phpZip)
    Write-Host "✅ PHP downloaded successfully" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to download PHP: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Trying alternative method..." -ForegroundColor Yellow
    
    # Alternative: Use curl if available
    try {
        & curl -L -o $phpZip "https://windows.php.net/downloads/releases/php-8.2.26-Win32-vs16-x64.zip"
        Write-Host "✅ PHP downloaded with curl" -ForegroundColor Green
    } catch {
        Write-Host "❌ Failed to download PHP with curl" -ForegroundColor Red
        Write-Host "Please download PHP manually from: https://windows.php.net/download/" -ForegroundColor Yellow
        exit 1
    }
}

# Extract PHP
if (Test-Path $phpZip) {
    Write-Host "2. Extracting PHP..." -ForegroundColor Cyan
    try {
        Expand-Archive -Path $phpZip -DestinationPath $phpDir -Force
        Write-Host "✅ PHP extracted successfully" -ForegroundColor Green
    } catch {
        Write-Host "❌ Failed to extract PHP: $($_.Exception.Message)" -ForegroundColor Red
        exit 1
    }
} else {
    Write-Host "❌ PHP zip file not found" -ForegroundColor Red
    exit 1
}

# Configure PHP
Write-Host "3. Configuring PHP..." -ForegroundColor Cyan

$phpIni = "$phpDir/php.ini"
$phpIniDev = "$phpDir/php.ini-development"

if (Test-Path $phpIniDev) {
    Copy-Item $phpIniDev $phpIni -Force
    Write-Host "✅ PHP configuration copied" -ForegroundColor Green
} else {
    Write-Host "⚠️ php.ini-development not found, creating basic config" -ForegroundColor Yellow
    
    $basicConfig = @"
; WeBot PHP Configuration
memory_limit = 512M
max_execution_time = 300
upload_max_filesize = 10M
post_max_size = 10M
date.timezone = Asia/Tehran

; Extensions
extension=curl
extension=fileinfo
extension=gd
extension=intl
extension=mbstring
extension=openssl
extension=pdo_mysql
extension=zip
extension=json

; Error reporting
display_errors = On
error_reporting = E_ALL
log_errors = On
error_log = error.log

; Session
session.save_handler = files
session.save_path = "tmp"

; Security
allow_url_fopen = On
allow_url_include = Off
"@
    
    $basicConfig | Out-File -FilePath $phpIni -Encoding UTF8
    Write-Host "✅ Basic PHP configuration created" -ForegroundColor Green
}

# Create tmp directory
$tmpDir = "$phpDir/tmp"
if (!(Test-Path $tmpDir)) {
    New-Item -ItemType Directory -Path $tmpDir -Force | Out-Null
    Write-Host "✅ Temp directory created" -ForegroundColor Green
}

# Test PHP
Write-Host "4. Testing PHP installation..." -ForegroundColor Cyan

$phpExe = "$phpDir/php.exe"
if (Test-Path $phpExe) {
    try {
        $phpVersion = & $phpExe --version
        Write-Host "✅ PHP is working:" -ForegroundColor Green
        Write-Host $phpVersion[0] -ForegroundColor Gray
    } catch {
        Write-Host "❌ PHP test failed: $($_.Exception.Message)" -ForegroundColor Red
        exit 1
    }
} else {
    Write-Host "❌ php.exe not found" -ForegroundColor Red
    exit 1
}

# Test Composer
Write-Host "5. Testing Composer..." -ForegroundColor Cyan

if (Test-Path "composer.phar") {
    try {
        $composerVersion = & $phpExe composer.phar --version
        Write-Host "✅ Composer is working:" -ForegroundColor Green
        Write-Host $composerVersion -ForegroundColor Gray
    } catch {
        Write-Host "❌ Composer test failed: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "❌ composer.phar not found" -ForegroundColor Red
}

# Install dependencies
Write-Host "6. Installing PHP dependencies..." -ForegroundColor Cyan

if (Test-Path "composer.json") {
    try {
        & $phpExe composer.phar install --no-interaction --prefer-dist
        Write-Host "✅ Dependencies installed successfully" -ForegroundColor Green
    } catch {
        Write-Host "❌ Failed to install dependencies: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "You can try manually: php-portable/php.exe composer.phar install" -ForegroundColor Yellow
    }
} else {
    Write-Host "❌ composer.json not found" -ForegroundColor Red
}

# Create batch files for easy access
Write-Host "7. Creating helper scripts..." -ForegroundColor Cyan

$phpBat = @"
@echo off
"%~dp0php-portable\php.exe" %*
"@

$composerBat = @"
@echo off
"%~dp0php-portable\php.exe" "%~dp0composer.phar" %*
"@

$phpBat | Out-File -FilePath "php.bat" -Encoding ASCII
$composerBat | Out-File -FilePath "composer.bat" -Encoding ASCII

Write-Host "✅ Helper scripts created (php.bat, composer.bat)" -ForegroundColor Green

# Summary
Write-Host ""
Write-Host "=== Installation Summary ===" -ForegroundColor Green
Write-Host "PHP installed in: $phpDir" -ForegroundColor White
Write-Host "Configuration file: $phpIni" -ForegroundColor White
Write-Host ""
Write-Host "Usage:" -ForegroundColor Cyan
Write-Host "  php.bat --version" -ForegroundColor White
Write-Host "  composer.bat install" -ForegroundColor White
Write-Host "  php.bat scripts/test-runner.php" -ForegroundColor White
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Cyan
Write-Host "1. Configure .env file" -ForegroundColor White
Write-Host "2. Run: composer.bat install" -ForegroundColor White
Write-Host "3. Run: php.bat scripts/test-runner.php" -ForegroundColor White
Write-Host "4. Run tests: php.bat vendor/bin/phpunit" -ForegroundColor White
Write-Host ""
Write-Host "Installation completed!" -ForegroundColor Green
