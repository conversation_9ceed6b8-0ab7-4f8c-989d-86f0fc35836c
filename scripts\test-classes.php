<?php

declare(strict_types=1);

/**
 * WeBot Class Test Script
 * 
 * Tests if all classes can be loaded without errors.
 * 
 * @package WeBot\Scripts
 * @version 2.0
 */

// Colors for output
const COLOR_RED = "\033[31m";
const COLOR_GREEN = "\033[32m";
const COLOR_YELLOW = "\033[33m";
const COLOR_BLUE = "\033[34m";
const COLOR_RESET = "\033[0m";

/**
 * Print colored message
 */
function printMessage(string $message, string $color = COLOR_RESET): void
{
    echo $color . $message . COLOR_RESET . PHP_EOL;
}

/**
 * Test class loading
 */
function testClassLoading(): int
{
    printMessage("🔍 WeBot Class Loading Test", COLOR_BLUE);
    printMessage("===========================", COLOR_BLUE);
    printMessage("");
    
    $classes = [
        'WeBot\\Core\\Cache\\CacheManager',
        'WeBot\\Core\\Cache\\Contracts\\CacheInterface',
        'WeBot\\Core\\Cache\\Drivers\\RedisDriver',
        'WeBot\\Core\\Cache\\Managers\\CacheTagManager',
        'WeBot\\Core\\Cache\\Managers\\CacheSessionManager',
        'WeBot\\Core\\Cache\\Managers\\CacheStatsManager',
        'WeBot\\Core\\Cache\\Managers\\CacheSpecializedManager',
        'WeBot\\Core\\RateLimiter',
        'WeBot\\Middleware\\InputValidationMiddleware'
    ];
    
    $totalClasses = count($classes);
    $loadedClasses = 0;
    $errors = [];
    
    foreach ($classes as $className) {
        try {
            if (class_exists($className) || interface_exists($className)) {
                printMessage("  ✅ $className", COLOR_GREEN);
                $loadedClasses++;
            } else {
                printMessage("  ❌ $className - Class not found", COLOR_RED);
                $errors[] = "$className - Class not found";
            }
        } catch (Throwable $e) {
            printMessage("  ❌ $className - " . $e->getMessage(), COLOR_RED);
            $errors[] = "$className - " . $e->getMessage();
        }
    }
    
    printMessage("");
    printMessage("📊 Summary", COLOR_BLUE);
    printMessage("----------", COLOR_BLUE);
    printMessage("Total classes tested: $totalClasses");
    printMessage("Successfully loaded: $loadedClasses");
    printMessage("Failed to load: " . ($totalClasses - $loadedClasses));
    printMessage("Success rate: " . round(($loadedClasses / $totalClasses) * 100, 2) . "%");
    
    if (empty($errors)) {
        printMessage("🎉 All classes loaded successfully!", COLOR_GREEN);
        return 0;
    } else {
        printMessage("💥 Classes with errors:", COLOR_RED);
        foreach ($errors as $error) {
            printMessage("  - $error", COLOR_RED);
        }
        return 1;
    }
}

/**
 * Test basic functionality
 */
function testBasicFunctionality(): int
{
    printMessage("🧪 Testing Basic Functionality", COLOR_BLUE);
    printMessage("==============================", COLOR_BLUE);
    printMessage("");
    
    $tests = [];
    
    // Test CacheInterface
    try {
        $interface = new ReflectionClass('WeBot\\Core\\Cache\\Contracts\\CacheInterface');
        $methods = $interface->getMethods();
        $tests['CacheInterface'] = count($methods) > 0 ? 'OK' : 'No methods found';
        printMessage("  ✅ CacheInterface - " . count($methods) . " methods", COLOR_GREEN);
    } catch (Throwable $e) {
        $tests['CacheInterface'] = 'Error: ' . $e->getMessage();
        printMessage("  ❌ CacheInterface - " . $e->getMessage(), COLOR_RED);
    }
    
    // Test CacheManager
    try {
        $class = new ReflectionClass('WeBot\\Core\\Cache\\CacheManager');
        $constructor = $class->getConstructor();
        $tests['CacheManager'] = $constructor ? 'OK' : 'No constructor';
        printMessage("  ✅ CacheManager - Constructor found", COLOR_GREEN);
    } catch (Throwable $e) {
        $tests['CacheManager'] = 'Error: ' . $e->getMessage();
        printMessage("  ❌ CacheManager - " . $e->getMessage(), COLOR_RED);
    }
    
    // Test RateLimiter
    try {
        $class = new ReflectionClass('WeBot\\Core\\RateLimiter');
        $methods = $class->getMethods(ReflectionMethod::IS_PUBLIC);
        $methodNames = array_map(fn($m) => $m->getName(), $methods);
        
        $requiredMethods = ['isAllowed', 'getRemainingAttempts', 'getTimeUntilReset', 'banUser'];
        $missingMethods = array_diff($requiredMethods, $methodNames);
        
        if (empty($missingMethods)) {
            $tests['RateLimiter'] = 'OK';
            printMessage("  ✅ RateLimiter - All required methods found", COLOR_GREEN);
        } else {
            $tests['RateLimiter'] = 'Missing methods: ' . implode(', ', $missingMethods);
            printMessage("  ❌ RateLimiter - Missing methods: " . implode(', ', $missingMethods), COLOR_RED);
        }
    } catch (Throwable $e) {
        $tests['RateLimiter'] = 'Error: ' . $e->getMessage();
        printMessage("  ❌ RateLimiter - " . $e->getMessage(), COLOR_RED);
    }
    
    printMessage("");
    
    $successCount = count(array_filter($tests, fn($result) => $result === 'OK'));
    $totalCount = count($tests);
    
    if ($successCount === $totalCount) {
        printMessage("🎉 All functionality tests passed!", COLOR_GREEN);
        return 0;
    } else {
        printMessage("💥 Some functionality tests failed", COLOR_RED);
        return 1;
    }
}

/**
 * Main execution
 */
function main(): int
{
    // Try to load autoloader
    $autoloadPaths = [
        __DIR__ . '/../vendor/autoload.php',
        __DIR__ . '/../autoload.php'
    ];
    
    $autoloaderFound = false;
    foreach ($autoloadPaths as $path) {
        if (file_exists($path)) {
            require_once $path;
            $autoloaderFound = true;
            printMessage("✅ Autoloader found: $path", COLOR_GREEN);
            break;
        }
    }
    
    if (!$autoloaderFound) {
        printMessage("⚠️  No autoloader found, testing without dependencies", COLOR_YELLOW);
        printMessage("   Run 'composer install' for full testing", COLOR_YELLOW);
        printMessage("");
    }
    
    $result1 = testClassLoading();
    printMessage("");
    $result2 = testBasicFunctionality();
    
    return max($result1, $result2);
}

// Run the test
exit(main());
