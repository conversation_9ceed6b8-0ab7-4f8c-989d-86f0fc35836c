# WeBot Simple Manual Testing Script
Write-Host "=== WeBot Simple Manual Testing Script ===" -ForegroundColor Green
Write-Host "Running basic project validation..." -ForegroundColor Yellow
Write-Host ""

# Test 1: Check essential files
Write-Host "1. Essential Files Check:" -ForegroundColor Cyan

$essentialFiles = @(
    "composer.json",
    "phpunit.xml", 
    ".env.example",
    "src/Core/TelegramBot.php",
    "src/Core/Database.php",
    "src/Core/Config.php",
    "public/index.php",
    "public/webhook.php"
)

$missingCount = 0
foreach ($file in $essentialFiles) {
    if (Test-Path $file) {
        $size = (Get-Item $file).Length
        Write-Host "  ✅ $file ($size bytes)" -ForegroundColor Green
    } else {
        Write-Host "  ❌ $file (missing)" -ForegroundColor Red
        $missingCount++
    }
}

$completeness = [math]::Round((($essentialFiles.Count - $missingCount) / $essentialFiles.Count) * 100, 1)
Write-Host "  Completeness: $completeness%" -ForegroundColor $(if ($completeness -gt 80) { "Green" } elseif ($completeness -gt 50) { "Yellow" } else { "Red" })
Write-Host ""

# Test 2: Check composer.json
Write-Host "2. Composer Configuration:" -ForegroundColor Cyan

if (Test-Path "composer.json") {
    try {
        $composerContent = Get-Content "composer.json" -Raw
        $composer = $composerContent | ConvertFrom-Json
        
        Write-Host "  ✅ Valid JSON format" -ForegroundColor Green
        Write-Host "  Package: $($composer.name)" -ForegroundColor Gray
        
        if ($composer.require.php) {
            Write-Host "  PHP requirement: $($composer.require.php)" -ForegroundColor Gray
        }
        
        $depCount = 0
        if ($composer.require) {
            $depCount = ($composer.require | Get-Member -MemberType NoteProperty).Count
        }
        Write-Host "  Dependencies: $depCount" -ForegroundColor Gray
        
    } catch {
        Write-Host "  ❌ Invalid JSON: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "  ❌ composer.json not found" -ForegroundColor Red
}
Write-Host ""

# Test 3: Check source code
Write-Host "3. Source Code Analysis:" -ForegroundColor Cyan

if (Test-Path "src") {
    $phpFiles = Get-ChildItem -Path "src" -Recurse -Filter "*.php" -ErrorAction SilentlyContinue
    if ($phpFiles) {
        Write-Host "  ✅ PHP files found: $($phpFiles.Count)" -ForegroundColor Green
        
        $totalLines = 0
        foreach ($file in $phpFiles) {
            $content = Get-Content $file.FullName -ErrorAction SilentlyContinue
            if ($content) {
                $totalLines += $content.Count
            }
        }
        Write-Host "  Total lines of code: $totalLines" -ForegroundColor Gray
        
    } else {
        Write-Host "  ❌ No PHP files found" -ForegroundColor Red
    }
} else {
    Write-Host "  ❌ src/ directory not found" -ForegroundColor Red
}
Write-Host ""

# Test 4: Check tests
Write-Host "4. Test Files Analysis:" -ForegroundColor Cyan

if (Test-Path "tests") {
    $testFiles = Get-ChildItem -Path "tests" -Recurse -Filter "*Test.php" -ErrorAction SilentlyContinue
    if ($testFiles) {
        Write-Host "  ✅ Test files found: $($testFiles.Count)" -ForegroundColor Green
        
        $unitTests = $testFiles | Where-Object { $_.FullName -like "*Unit*" }
        $integrationTests = $testFiles | Where-Object { $_.FullName -like "*Integration*" }
        
        Write-Host "  Unit tests: $($unitTests.Count)" -ForegroundColor Gray
        Write-Host "  Integration tests: $($integrationTests.Count)" -ForegroundColor Gray
        
    } else {
        Write-Host "  ⚠️ No test files found" -ForegroundColor Yellow
    }
} else {
    Write-Host "  ❌ tests/ directory not found" -ForegroundColor Red
}
Write-Host ""

# Test 5: Check migrations
Write-Host "5. Database Migrations:" -ForegroundColor Cyan

if (Test-Path "migrations") {
    $migrationFiles = Get-ChildItem "migrations" -Filter "*.sql" -ErrorAction SilentlyContinue
    if ($migrationFiles) {
        Write-Host "  ✅ Migration files: $($migrationFiles.Count)" -ForegroundColor Green
        foreach ($migration in $migrationFiles) {
            Write-Host "    - $($migration.Name)" -ForegroundColor Gray
        }
    } else {
        Write-Host "  ⚠️ No migration files found" -ForegroundColor Yellow
    }
} else {
    Write-Host "  ❌ migrations/ directory not found" -ForegroundColor Red
}
Write-Host ""

# Test 6: Check configuration
Write-Host "6. Configuration Files:" -ForegroundColor Cyan

$configFiles = @(".env.example", ".env", ".env.testing", "phpunit.xml")
foreach ($file in $configFiles) {
    if (Test-Path $file) {
        Write-Host "  ✅ $file" -ForegroundColor Green
    } else {
        Write-Host "  ❌ $file (missing)" -ForegroundColor Red
    }
}
Write-Host ""

# Test 7: Check Docker files
Write-Host "7. Docker Configuration:" -ForegroundColor Cyan

$dockerFiles = @("Dockerfile", "docker-compose.yml", "docker-compose.test.yml")
foreach ($file in $dockerFiles) {
    if (Test-Path $file) {
        Write-Host "  ✅ $file" -ForegroundColor Green
    } else {
        Write-Host "  ❌ $file (missing)" -ForegroundColor Red
    }
}
Write-Host ""

# Test 8: Check documentation
Write-Host "8. Documentation:" -ForegroundColor Cyan

$docFiles = @("README.md", "docs/TESTING_AND_DEBUGGING_GUIDE.md")
foreach ($file in $docFiles) {
    if (Test-Path $file) {
        Write-Host "  ✅ $file" -ForegroundColor Green
    } else {
        Write-Host "  ❌ $file (missing)" -ForegroundColor Red
    }
}
Write-Host ""

# Summary
Write-Host "=== Summary ===" -ForegroundColor Green
Write-Host "Project completeness: $completeness%" -ForegroundColor White
Write-Host ""
Write-Host "Next steps for testing:" -ForegroundColor Cyan
Write-Host "1. Install PHP 8.2+ and Composer" -ForegroundColor White
Write-Host "2. Run: composer install" -ForegroundColor White
Write-Host "3. Configure .env file" -ForegroundColor White
Write-Host "4. Run: vendor/bin/phpunit" -ForegroundColor White
Write-Host ""
Write-Host "Test completed at: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor Gray
Write-Host "=========================" -ForegroundColor Green
