<?php

declare(strict_types=1);

namespace WeBot\Tests\Unit\Security;

use WeBot\Tests\Unit\BaseTestCase;
use WeBot\Core\InputValidator;
use WeBot\Security\SessionManager;
use WeBot\Security\FileUploadValidator;
use WeBot\Middleware\RateLimitMiddleware;

/**
 * Security Component Tests
 * 
 * Tests for all security-related components including
 * input validation, session management, file uploads, and rate limiting.
 * 
 * @package WeBot\Tests\Unit\Security
 * @version 2.0
 */
class SecurityTest extends BaseTestCase
{
    private InputValidator $inputValidator;
    private SessionManager $sessionManager;
    private FileUploadValidator $fileValidator;
    private RateLimitMiddleware $rateLimitMiddleware;
    
    protected function setUp(): void
    {
        parent::setUp();
        $this->inputValidator = new InputValidator();
        $this->sessionManager = SessionManager::getInstance();
        $this->fileValidator = new FileUploadValidator();
        $this->rateLimitMiddleware = new RateLimitMiddleware();
    }
    
    /**
     * Test SQL injection detection
     */
    public function testSqlInjectionDetection(): void
    {
        $maliciousInputs = [
            "'; DROP TABLE users; --",
            "1' OR '1'='1",
            "admin'/*",
            "1; DELETE FROM users WHERE 1=1; --",
            "' UNION SELECT * FROM users --",
            "1' AND (SELECT COUNT(*) FROM users) > 0 --"
        ];
        
        foreach ($maliciousInputs as $input) {
            $result = $this->inputValidator->validate(['test_field' => $input], [
                'test_field' => 'required|string'
            ]);
            
            $this->assertFalse($result['valid'], "SQL injection not detected: {$input}");
            $this->assertStringContains('SQL injection', implode(' ', $result['errors']));
        }
    }
    
    /**
     * Test XSS attack detection
     */
    public function testXssDetection(): void
    {
        $xssInputs = [
            '<script>alert("XSS")</script>',
            '<img src="x" onerror="alert(1)">',
            '<iframe src="javascript:alert(1)"></iframe>',
            '<svg onload="alert(1)">',
            '<body onload="alert(1)">',
            'javascript:alert(1)',
            '<script src="http://evil.com/xss.js"></script>'
        ];
        
        foreach ($xssInputs as $input) {
            $result = $this->inputValidator->validate(['test_field' => $input], [
                'test_field' => 'required|string'
            ]);
            
            $this->assertFalse($result['valid'], "XSS not detected: {$input}");
            $this->assertStringContains('XSS', implode(' ', $result['errors']));
        }
    }
    
    /**
     * Test safe input validation
     */
    public function testSafeInputValidation(): void
    {
        $safeInputs = [
            'normal text',
            '<EMAIL>',
            '+1234567890',
            'Test User Name',
            '123456',
            'https://example.com'
        ];
        
        foreach ($safeInputs as $input) {
            $result = $this->inputValidator->validate(['test_field' => $input], [
                'test_field' => 'required|string'
            ]);
            
            $this->assertTrue($result['valid'], "Safe input rejected: {$input}");
        }
    }
    
    /**
     * Test Telegram ID validation
     */
    public function testTelegramIdValidation(): void
    {
        // Valid Telegram IDs
        $validIds = ['123456789', '987654321', '555666777'];
        
        foreach ($validIds as $id) {
            $result = $this->inputValidator->validate(['telegram_id' => $id], [
                'telegram_id' => 'required|telegram_id'
            ]);
            
            $this->assertTrue($result['valid'], "Valid Telegram ID rejected: {$id}");
        }
        
        // Invalid Telegram IDs
        $invalidIds = ['abc', '123', '12345678901234567890', '-123', '0'];
        
        foreach ($invalidIds as $id) {
            $result = $this->inputValidator->validate(['telegram_id' => $id], [
                'telegram_id' => 'required|telegram_id'
            ]);
            
            $this->assertFalse($result['valid'], "Invalid Telegram ID accepted: {$id}");
        }
    }
    
    /**
     * Test phone number validation
     */
    public function testPhoneNumberValidation(): void
    {
        // Valid phone numbers
        $validPhones = ['+1234567890', '1234567890', '+989123456789'];
        
        foreach ($validPhones as $phone) {
            $result = $this->inputValidator->validate(['phone' => $phone], [
                'phone' => 'required|phone'
            ]);
            
            $this->assertTrue($result['valid'], "Valid phone rejected: {$phone}");
        }
        
        // Invalid phone numbers
        $invalidPhones = ['123', 'abc123', '+123abc', ''];
        
        foreach ($invalidPhones as $phone) {
            $result = $this->inputValidator->validate(['phone' => $phone], [
                'phone' => 'required|phone'
            ]);
            
            $this->assertFalse($result['valid'], "Invalid phone accepted: {$phone}");
        }
    }
    
    /**
     * Test session security
     */
    public function testSessionSecurity(): void
    {
        // Test CSRF token generation
        $token1 = $this->sessionManager->generateCsrfToken();
        $token2 = $this->sessionManager->generateCsrfToken();
        
        $this->assertNotNull($token1);
        $this->assertNotNull($token2);
        $this->assertEquals($token1, $token2); // Should be same in same session
        $this->assertTrue(strlen($token1) >= 32); // Should be long enough
        
        // Test CSRF token validation
        $this->assertTrue($this->sessionManager->validateCsrfToken($token1));
        $this->assertFalse($this->sessionManager->validateCsrfToken('invalid_token'));
        $this->assertFalse($this->sessionManager->validateCsrfToken(''));
    }
    
    /**
     * Test file upload validation
     */
    public function testFileUploadValidation(): void
    {
        // Test valid file
        $validFile = [
            'name' => 'test.jpg',
            'type' => 'image/jpeg',
            'size' => 1024 * 1024, // 1MB
            'tmp_name' => '/tmp/test_upload',
            'error' => UPLOAD_ERR_OK
        ];
        
        // Mock file_exists and is_uploaded_file for testing
        $GLOBALS['test_mode'] = true;
        
        // Test dangerous file extensions
        $dangerousFiles = [
            ['name' => 'malware.php', 'type' => 'application/x-php'],
            ['name' => 'script.js', 'type' => 'application/javascript'],
            ['name' => 'virus.exe', 'type' => 'application/x-executable'],
            ['name' => 'hack.bat', 'type' => 'application/x-bat']
        ];
        
        foreach ($dangerousFiles as $file) {
            $testFile = array_merge($validFile, $file);
            $result = $this->fileValidator->validate($testFile);
            
            $this->assertFalse($result['valid'], "Dangerous file accepted: {$file['name']}");
        }
        
        // Test file size limits
        $oversizedFile = array_merge($validFile, ['size' => 50 * 1024 * 1024]); // 50MB
        $result = $this->fileValidator->validate($oversizedFile);
        
        $this->assertFalse($result['valid'], "Oversized file accepted");
        $this->assertStringContains('size', implode(' ', $result['errors']));
    }
    
    /**
     * Test rate limiting functionality
     */
    public function testRateLimiting(): void
    {
        $request = [
            'from' => ['id' => 123456789],
            'chat' => ['id' => 123456789],
            'ip' => '127.0.0.1'
        ];
        
        $nextFunction = function($req) {
            return ['status' => 'success', 'data' => 'processed'];
        };
        
        // First few requests should pass
        for ($i = 0; $i < 5; $i++) {
            $result = $this->rateLimitMiddleware->handle($request, $nextFunction);
            $this->assertEquals('success', $result['status']);
        }
        
        // After many requests, should be rate limited
        for ($i = 0; $i < 60; $i++) {
            $result = $this->rateLimitMiddleware->handle($request, $nextFunction);
        }
        
        // This should trigger rate limiting
        $result = $this->rateLimitMiddleware->handle($request, $nextFunction);
        $this->assertStringContains('تعداد درخواست', $result['text']);
    }
    
    /**
     * Test password security (if applicable)
     */
    public function testPasswordSecurity(): void
    {
        // Test weak passwords
        $weakPasswords = [
            '123456',
            'password',
            'admin',
            'qwerty',
            '12345678'
        ];
        
        foreach ($weakPasswords as $password) {
            $result = $this->inputValidator->validate(['password' => $password], [
                'password' => 'required|string|min:8'
            ]);
            
            // Should fail minimum length requirement
            if (strlen($password) < 8) {
                $this->assertFalse($result['valid'], "Weak password accepted: {$password}");
            }
        }
    }
    
    /**
     * Test input sanitization
     */
    public function testInputSanitization(): void
    {
        $dirtyInputs = [
            '<script>alert("test")</script>Hello',
            'Test & Company',
            'Price: $100 < $200',
            'Email: <EMAIL>'
        ];
        
        foreach ($dirtyInputs as $input) {
            $result = $this->inputValidator->validate(['content' => $input], [
                'content' => 'required|safe_html'
            ]);
            
            if ($result['valid']) {
                // Should be sanitized
                $sanitized = $result['data']['content'];
                $this->assertStringNotContains('<script>', $sanitized);
            }
        }
    }
    
    /**
     * Test security headers
     */
    public function testSecurityHeaders(): void
    {
        // This would typically test HTTP headers in a real web environment
        // For now, we'll test the configuration
        
        $expectedHeaders = [
            'X-Frame-Options',
            'X-Content-Type-Options',
            'X-XSS-Protection',
            'Content-Security-Policy',
            'Strict-Transport-Security'
        ];
        
        // In a real test, you would check if these headers are set
        foreach ($expectedHeaders as $header) {
            $this->assertNotNull($header, "Security header should be defined: {$header}");
        }
    }
    
    /**
     * Test data encryption/decryption (if implemented)
     */
    public function testDataEncryption(): void
    {
        $sensitiveData = 'sensitive_user_data_123';
        
        // Test that sensitive data is not stored in plain text
        // This is a placeholder for actual encryption tests
        $this->assertNotEquals($sensitiveData, hash('sha256', $sensitiveData));
        
        // Test that hashed data is consistent
        $hash1 = hash('sha256', $sensitiveData);
        $hash2 = hash('sha256', $sensitiveData);
        $this->assertEquals($hash1, $hash2);
    }
}
