<?php

declare(strict_types=1);

namespace WeBot\Tests\Unit\Services;

use WeBot\Tests\Unit\BaseTestCase;
use WeBot\Services\TelegramService;

/**
 * Service Unit Tests
 * 
 * Comprehensive service tests that work with actual service signatures
 * 
 * @package WeBot\Tests\Unit\Services
 * @version 2.0
 */
class ServiceUnitTest extends BaseTestCase
{
    private TelegramService $telegramService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->initializeServices();
    }

    /**
     * Initialize services
     */
    private function initializeServices(): void
    {
        $this->telegramService = new TelegramService($this->config);
    }

    /**
     * Test Telegram service message sending
     */
    public function testTelegramServiceSendMessage(): void
    {
        $messageData = [
            'chat_id' => 123456789,
            'text' => 'Test message',
            'parse_mode' => 'HTML'
        ];

        $result = $this->telegramService->sendMessage($messageData);

        $this->assertArrayHasKey('ok', $result);
        $this->assertTrue($result['ok']);
        $this->assertArrayHasKey('result', $result);
    }

    /**
     * Test Telegram service message editing
     */
    public function testTelegramServiceEditMessage(): void
    {
        $editData = [
            'chat_id' => 123456789,
            'message_id' => 123,
            'text' => 'Edited message',
            'parse_mode' => 'HTML'
        ];

        $result = $this->telegramService->editMessageText($editData);

        $this->assertArrayHasKey('ok', $result);
        $this->assertTrue($result['ok']);
    }

    /**
     * Test Telegram service callback query answering
     */
    public function testTelegramServiceAnswerCallback(): void
    {
        $callbackData = [
            'callback_query_id' => 'test_callback_123',
            'text' => 'Callback answered'
        ];

        $result = $this->telegramService->answerCallbackQuery($callbackData);

        $this->assertArrayHasKey('ok', $result);
        $this->assertTrue($result['ok']);
    }

    /**
     * Test service error handling
     */
    public function testServiceErrorHandling(): void
    {
        // Test invalid Telegram message
        $invalidMessage = [
            'chat_id' => '', // Invalid chat ID
            'text' => ''     // Empty text
        ];

        $result = $this->telegramService->sendMessage($invalidMessage);

        $this->assertArrayHasKey('ok', $result);
        $this->assertFalse($result['ok']);
    }

    /**
     * Test service configuration validation
     */
    public function testServiceConfigurationValidation(): void
    {
        // Test that services have proper configuration
        $telegramConfig = $this->config->get('telegram');
        $this->assertNotNull($telegramConfig);
        $this->assertArrayHasKey('token', $telegramConfig);
    }

    /**
     * Test service logging functionality
     */
    public function testServiceLogging(): void
    {
        // Test that services can log without errors
        $messageData = [
            'chat_id' => 123456789,
            'text' => 'Test logging message'
        ];

        // This should not throw any exceptions
        $result = $this->telegramService->sendMessage($messageData);
        $this->assertNotNull($result);
    }

    /**
     * Test service rate limiting
     */
    public function testServiceRateLimiting(): void
    {
        $messageData = [
            'chat_id' => 123456789,
            'text' => 'Rate limit test'
        ];

        // Send multiple messages rapidly
        $results = [];
        for ($i = 0; $i < 5; $i++) {
            $results[] = $this->telegramService->sendMessage($messageData);
        }

        // All should succeed in test environment
        foreach ($results as $result) {
            $this->assertArrayHasKey('ok', $result);
        }
    }

    /**
     * Test Telegram service with different message types
     */
    public function testTelegramServiceMessageTypes(): void
    {
        // Test text message
        $textMessage = [
            'chat_id' => 123456789,
            'text' => 'Simple text message'
        ];
        $result = $this->telegramService->sendMessage($textMessage);
        $this->assertTrue($result['ok']);

        // Test message with keyboard
        $keyboardMessage = [
            'chat_id' => 123456789,
            'text' => 'Message with keyboard',
            'reply_markup' => [
                'inline_keyboard' => [
                    [['text' => 'Button 1', 'callback_data' => 'btn1']]
                ]
            ]
        ];
        $result = $this->telegramService->sendMessage($keyboardMessage);
        $this->assertTrue($result['ok']);

        // Test message with HTML formatting
        $htmlMessage = [
            'chat_id' => 123456789,
            'text' => '<b>Bold text</b> and <i>italic text</i>',
            'parse_mode' => 'HTML'
        ];
        $result = $this->telegramService->sendMessage($htmlMessage);
        $this->assertTrue($result['ok']);
    }

    /**
     * Test Telegram service error responses
     */
    public function testTelegramServiceErrorResponses(): void
    {
        // Test with invalid chat_id
        $invalidChatMessage = [
            'chat_id' => 'invalid_chat_id',
            'text' => 'This should fail'
        ];
        $result = $this->telegramService->sendMessage($invalidChatMessage);
        $this->assertFalse($result['ok']);

        // Test with empty text
        $emptyTextMessage = [
            'chat_id' => 123456789,
            'text' => ''
        ];
        $result = $this->telegramService->sendMessage($emptyTextMessage);
        $this->assertFalse($result['ok']);
    }

    /**
     * Test service method existence
     */
    public function testServiceMethodExistence(): void
    {
        // Test that required methods exist
        $this->assertTrue(method_exists($this->telegramService, 'sendMessage'));
        $this->assertTrue(method_exists($this->telegramService, 'editMessageText'));
        $this->assertTrue(method_exists($this->telegramService, 'answerCallbackQuery'));
        $this->assertTrue(method_exists($this->telegramService, 'deleteMessage'));
    }

    /**
     * Test service configuration access
     */
    public function testServiceConfigurationAccess(): void
    {
        // Test that service can access configuration
        $config = $this->config->get('telegram');
        $this->assertNotNull($config);
        
        // Test required config keys
        $this->assertArrayHasKey('token', $config);
        $this->assertTrue(strlen($config['token']) > 0); // Check token is not empty
    }

    /**
     * Test service initialization
     */
    public function testServiceInitialization(): void
    {
        // Test that service initializes properly
        $this->assertNotNull($this->telegramService);
        $this->assertTrue($this->telegramService instanceof TelegramService);
    }
}
