<?php

declare(strict_types=1);

/**
 * WeBot Syntax Test Script
 * 
 * Tests PHP syntax for all files in the project.
 * 
 * @package WeBot\Scripts
 * @version 2.0
 */

// Colors for output
const COLOR_RED = "\033[31m";
const COLOR_GREEN = "\033[32m";
const COLOR_YELLOW = "\033[33m";
const COLOR_BLUE = "\033[34m";
const COLOR_RESET = "\033[0m";

/**
 * Print colored message
 */
function printMessage(string $message, string $color = COLOR_RESET): void
{
    echo $color . $message . COLOR_RESET . PHP_EOL;
}

/**
 * Test PHP syntax for a file
 */
function testFileSyntax(string $file): bool
{
    $output = [];
    $returnCode = 0;
    
    exec("php -l \"$file\" 2>&1", $output, $returnCode);
    
    return $returnCode === 0;
}

/**
 * Get all PHP files in directory
 */
function getPhpFiles(string $directory): array
{
    $files = [];
    $iterator = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($directory, RecursiveDirectoryIterator::SKIP_DOTS)
    );
    
    foreach ($iterator as $file) {
        if ($file->getExtension() === 'php') {
            $files[] = $file->getPathname();
        }
    }
    
    return $files;
}

/**
 * Main execution
 */
function main(): int
{
    printMessage("🔍 WeBot Syntax Test", COLOR_BLUE);
    printMessage("====================", COLOR_BLUE);
    printMessage("");
    
    $directories = ['src', 'tests', 'config', 'scripts'];
    $totalFiles = 0;
    $errorFiles = 0;
    $errors = [];
    
    foreach ($directories as $directory) {
        if (!is_dir($directory)) {
            printMessage("⚠️  Directory $directory not found, skipping", COLOR_YELLOW);
            continue;
        }
        
        printMessage("📁 Testing $directory/", COLOR_BLUE);
        
        $files = getPhpFiles($directory);
        $totalFiles += count($files);
        
        foreach ($files as $file) {
            $relativePath = str_replace(getcwd() . DIRECTORY_SEPARATOR, '', $file);
            
            if (testFileSyntax($file)) {
                printMessage("  ✅ $relativePath", COLOR_GREEN);
            } else {
                printMessage("  ❌ $relativePath", COLOR_RED);
                $errors[] = $relativePath;
                $errorFiles++;
            }
        }
        
        printMessage("");
    }
    
    // Summary
    printMessage("📊 Summary", COLOR_BLUE);
    printMessage("----------", COLOR_BLUE);
    printMessage("Total files tested: $totalFiles");
    printMessage("Files with errors: $errorFiles");
    printMessage("Success rate: " . round((($totalFiles - $errorFiles) / $totalFiles) * 100, 2) . "%");
    
    if ($errorFiles === 0) {
        printMessage("🎉 All files passed syntax check!", COLOR_GREEN);
        return 0;
    } else {
        printMessage("💥 Files with syntax errors:", COLOR_RED);
        foreach ($errors as $error) {
            printMessage("  - $error", COLOR_RED);
        }
        return 1;
    }
}

// Check if PHP CLI is available
if (!function_exists('exec')) {
    printMessage("❌ exec() function is not available", COLOR_RED);
    exit(1);
}

// Run the test
exit(main());
