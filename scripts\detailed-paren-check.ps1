# Detailed Parentheses Check Script
param([string]$FilePath = "src/Core/InputValidator.php")

Write-Host "=== Detailed Parentheses Analysis ===" -ForegroundColor Green
Write-Host "Analyzing file: $FilePath" -ForegroundColor Yellow
Write-Host ""

if (!(Test-Path $FilePath)) {
    Write-Host "File not found: $FilePath" -ForegroundColor Red
    exit 1
}

$content = Get-Content $FilePath
$lineNumber = 0
$openParens = 0
$closeParens = 0
$issues = @()

# Track function/method boundaries
$inFunction = $false
$functionStack = @()

foreach ($line in $content) {
    $lineNumber++
    
    # Skip comments and strings for more accurate counting
    $cleanLine = $line
    
    # Remove single-line comments
    if ($cleanLine -match '//') {
        $commentPos = $cleanLine.IndexOf('//')
        $cleanLine = $cleanLine.Substring(0, $commentPos)
    }
    
    # Remove strings (basic approach)
    $cleanLine = $cleanLine -replace '"[^"]*"', '""'
    $cleanLine = $cleanLine -replace "'[^']*'", "''"
    
    # Count parentheses in cleaned line
    $lineOpenParens = ($cleanLine.ToCharArray() | Where-Object { $_ -eq '(' }).Count
    $lineCloseParens = ($cleanLine.ToCharArray() | Where-Object { $_ -eq ')' }).Count
    
    $openParens += $lineOpenParens
    $closeParens += $lineCloseParens
    
    # Track function boundaries
    if ($line -match 'function\s+\w+\s*\(') {
        $inFunction = $true
        $functionStack += @{
            Name = ($line -replace '.*function\s+(\w+).*', '$1')
            StartLine = $lineNumber
            OpenParens = $openParens
            CloseParens = $closeParens
        }
    }
    
    # Check for potential issues
    if ($lineOpenParens -ne $lineCloseParens) {
        $balance = $openParens - $closeParens
        
        $issues += @{
            Line = $lineNumber
            Content = $line.Trim()
            OpenParens = $lineOpenParens
            CloseParens = $lineCloseParens
            RunningBalance = $balance
            CleanedLine = $cleanLine.Trim()
        }
    }
    
    # Special check for regex patterns
    if ($line -match '/.*\\.*\(.*/' -and $line -match "'.*'|`".*`"") {
        Write-Host "Line $lineNumber - Regex pattern detected:" -ForegroundColor Cyan
        Write-Host "  Original: $($line.Trim())" -ForegroundColor Gray
        Write-Host "  Cleaned:  $($cleanLine.Trim())" -ForegroundColor Gray
        Write-Host ""
    }
}

Write-Host "Analysis Results:" -ForegroundColor Cyan
Write-Host "Total open parentheses: $openParens" -ForegroundColor White
Write-Host "Total close parentheses: $closeParens" -ForegroundColor White
Write-Host "Difference: $($openParens - $closeParens)" -ForegroundColor $(if ($openParens -eq $closeParens) { "Green" } else { "Red" })
Write-Host ""

if ($issues.Count -gt 0) {
    Write-Host "Lines with unbalanced parentheses:" -ForegroundColor Yellow
    
    $lastBalance = 0
    foreach ($issue in $issues) {
        $color = if ($issue.RunningBalance -eq 0) { "Green" } 
                elseif ($issue.RunningBalance -gt $lastBalance) { "Yellow" } 
                else { "Red" }
        
        Write-Host "Line $($issue.Line): ($($issue.OpenParens) open, $($issue.CloseParens) close, balance: $($issue.RunningBalance))" -ForegroundColor $color
        Write-Host "  Original: $($issue.Content)" -ForegroundColor Gray
        Write-Host "  Cleaned:  $($issue.CleanedLine)" -ForegroundColor DarkGray
        Write-Host ""
        
        $lastBalance = $issue.RunningBalance
    }
}

# Look for specific patterns that might cause issues
Write-Host "Checking for specific problematic patterns:" -ForegroundColor Cyan

$problematicLines = @()
$lineNumber = 0

foreach ($line in $content) {
    $lineNumber++
    
    # Check for incomplete function calls
    if ($line -match '\w+\s*\([^)]*$' -and !($line -match '//') -and !($line -match 'if\s*\(') -and !($line -match 'foreach\s*\(') -and !($line -match 'while\s*\(')) {
        $problematicLines += @{
            Line = $lineNumber
            Content = $line.Trim()
            Issue = "Possible incomplete function call"
        }
    }
    
    # Check for regex with unescaped parentheses in character classes
    if ($line -match '/\[.*\(.*\]/' -and !($line -match '/\[.*\\\(.*\]/')) {
        $problematicLines += @{
            Line = $lineNumber
            Content = $line.Trim()
            Issue = "Regex with unescaped parentheses in character class"
        }
    }
    
    # Check for array access with missing closing bracket
    if ($line -match '\$\w+\[[^\]]*$' -and !($line -match '//')) {
        $problematicLines += @{
            Line = $lineNumber
            Content = $line.Trim()
            Issue = "Possible incomplete array access"
        }
    }
}

if ($problematicLines.Count -gt 0) {
    Write-Host "Potentially problematic patterns found:" -ForegroundColor Yellow
    
    foreach ($problem in $problematicLines) {
        Write-Host "Line $($problem.Line): $($problem.Issue)" -ForegroundColor Red
        Write-Host "  $($problem.Content)" -ForegroundColor Gray
        Write-Host ""
    }
} else {
    Write-Host "No obvious problematic patterns found." -ForegroundColor Green
}

# Suggest fixes
if ($openParens -ne $closeParens) {
    $diff = $openParens - $closeParens
    Write-Host "Suggested fixes:" -ForegroundColor Cyan
    
    if ($diff -gt 0) {
        Write-Host "- Add $diff closing parenthesis(es) ')'" -ForegroundColor Yellow
        Write-Host "- Check the last few lines with unbalanced parentheses" -ForegroundColor Yellow
    } else {
        Write-Host "- Remove $([math]::Abs($diff)) closing parenthesis(es) ')'" -ForegroundColor Yellow
        Write-Host "- Check for extra closing parentheses" -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "Analysis completed." -ForegroundColor Green
