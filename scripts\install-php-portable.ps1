# Install PHP Portable Script
Write-Host "=== Installing PHP Portable ===" -ForegroundColor Green
Write-Host "Downloading and setting up PHP 8.2..." -ForegroundColor Yellow
Write-Host ""

# Create php directory
$phpDir = "php-portable"
if (!(Test-Path $phpDir)) {
    New-Item -ItemType Directory -Path $phpDir -Force | Out-Null
    Write-Host "Created directory: $phpDir" -ForegroundColor Green
}

# Download PHP
Write-Host "1. Downloading PHP 8.2..." -ForegroundColor Cyan

$phpUrl = "https://windows.php.net/downloads/releases/php-8.2.26-Win32-vs16-x64.zip"
$phpZip = "$phpDir/php.zip"

try {
    # Use .NET WebClient for better compatibility
    $webClient = New-Object System.Net.WebClient
    $webClient.Headers.Add("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
    
    Write-Host "Downloading from: $phpUrl" -ForegroundColor Gray
    $webClient.DownloadFile($phpUrl, $phpZip)
    
    if (Test-Path $phpZip) {
        $size = (Get-Item $phpZip).Length / 1MB
        Write-Host "✅ Downloaded PHP: $([math]::Round($size, 1)) MB" -ForegroundColor Green
    } else {
        throw "Download failed"
    }
} catch {
    Write-Host "❌ Failed to download PHP: $($_.Exception.Message)" -ForegroundColor Red
    
    # Try alternative URL
    Write-Host "Trying alternative download..." -ForegroundColor Yellow
    $phpUrl = "https://windows.php.net/downloads/releases/php-8.2.25-Win32-vs16-x64.zip"
    
    try {
        $webClient.DownloadFile($phpUrl, $phpZip)
        if (Test-Path $phpZip) {
            Write-Host "✅ Downloaded PHP from alternative source" -ForegroundColor Green
        } else {
            throw "Alternative download failed"
        }
    } catch {
        Write-Host "❌ All download attempts failed" -ForegroundColor Red
        Write-Host "Please download PHP manually from: https://windows.php.net/download/" -ForegroundColor Yellow
        exit 1
    }
}

# Extract PHP
Write-Host "2. Extracting PHP..." -ForegroundColor Cyan

try {
    # Use PowerShell's Expand-Archive
    Expand-Archive -Path $phpZip -DestinationPath $phpDir -Force
    Write-Host "✅ PHP extracted successfully" -ForegroundColor Green
    
    # Remove zip file
    Remove-Item $phpZip -Force -ErrorAction SilentlyContinue
    
} catch {
    Write-Host "❌ Failed to extract PHP: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Configure PHP
Write-Host "3. Configuring PHP..." -ForegroundColor Cyan

$phpExe = "$phpDir/php.exe"
if (Test-Path $phpExe) {
    Write-Host "✅ Found PHP executable: $phpExe" -ForegroundColor Green
    
    # Create php.ini
    $phpIni = "$phpDir/php.ini"
    $phpIniDev = "$phpDir/php.ini-development"
    
    if (Test-Path $phpIniDev) {
        Copy-Item $phpIniDev $phpIni -Force
        Write-Host "✅ Created php.ini from development template" -ForegroundColor Green
    } else {
        # Create basic php.ini
        $basicIni = @"
; WeBot PHP Configuration
memory_limit = 512M
max_execution_time = 300
upload_max_filesize = 10M
post_max_size = 10M
date.timezone = Asia/Tehran

; Extensions
extension=curl
extension=fileinfo
extension=gd
extension=intl
extension=mbstring
extension=openssl
extension=pdo_mysql
extension=zip
extension=json

; Error reporting
display_errors = On
error_reporting = E_ALL
log_errors = On

; Security
allow_url_fopen = On
allow_url_include = Off
"@
        $basicIni | Out-File -FilePath $phpIni -Encoding UTF8
        Write-Host "✅ Created basic php.ini" -ForegroundColor Green
    }
    
    # Test PHP
    Write-Host "4. Testing PHP..." -ForegroundColor Cyan
    
    try {
        $phpVersion = & $phpExe --version 2>$null | Select-Object -First 1
        Write-Host "✅ PHP Version: $phpVersion" -ForegroundColor Green
        
        # Test extensions
        $extensions = & $phpExe -m 2>$null
        $requiredExtensions = @('curl', 'json', 'mbstring', 'openssl')
        
        foreach ($ext in $requiredExtensions) {
            if ($extensions -contains $ext) {
                Write-Host "  ✅ $ext" -ForegroundColor Green
            } else {
                Write-Host "  ❌ $ext (missing)" -ForegroundColor Red
            }
        }
        
    } catch {
        Write-Host "❌ PHP test failed: $($_.Exception.Message)" -ForegroundColor Red
    }
    
} else {
    Write-Host "❌ PHP executable not found after extraction" -ForegroundColor Red
    exit 1
}

# Create helper scripts
Write-Host "5. Creating helper scripts..." -ForegroundColor Cyan

$phpBat = @"
@echo off
"%~dp0$phpDir\php.exe" %*
"@

$composerBat = @"
@echo off
"%~dp0$phpDir\php.exe" composer.phar %*
"@

$testBat = @"
@echo off
"%~dp0$phpDir\php.exe" test-runner.php
"@

$phpBat | Out-File -FilePath "php.bat" -Encoding ASCII
$composerBat | Out-File -FilePath "composer.bat" -Encoding ASCII
$testBat | Out-File -FilePath "test.bat" -Encoding ASCII

Write-Host "✅ Created helper scripts: php.bat, composer.bat, test.bat" -ForegroundColor Green

# Install Composer dependencies
Write-Host "6. Installing Composer dependencies..." -ForegroundColor Cyan

if (Test-Path "composer.phar") {
    try {
        Write-Host "Running: php composer.phar install..." -ForegroundColor Gray
        & $phpExe composer.phar install --no-interaction --prefer-dist
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Dependencies installed successfully" -ForegroundColor Green
        } else {
            Write-Host "⚠️ Dependencies installation had issues (exit code: $LASTEXITCODE)" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "❌ Failed to install dependencies: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "❌ composer.phar not found" -ForegroundColor Red
    Write-Host "Please download composer.phar from: https://getcomposer.org/download/" -ForegroundColor Yellow
}

# Final test
Write-Host "7. Final verification..." -ForegroundColor Cyan

if (Test-Path "vendor/autoload.php") {
    Write-Host "✅ Dependencies installed - vendor/autoload.php exists" -ForegroundColor Green
} else {
    Write-Host "❌ Dependencies not installed - vendor/autoload.php missing" -ForegroundColor Red
}

# Summary
Write-Host ""
Write-Host "=== Installation Summary ===" -ForegroundColor Green

if (Test-Path $phpExe) {
    Write-Host "✅ PHP installed at: $phpExe" -ForegroundColor Green
    Write-Host "✅ Helper scripts created" -ForegroundColor Green
    
    if (Test-Path "vendor/autoload.php") {
        Write-Host "✅ Dependencies installed" -ForegroundColor Green
        Write-Host ""
        Write-Host "🎉 Installation complete! You can now:" -ForegroundColor Green
        Write-Host "1. Run: php.bat --version" -ForegroundColor White
        Write-Host "2. Run: test.bat" -ForegroundColor White
        Write-Host "3. Run: php.bat vendor/bin/phpunit" -ForegroundColor White
    } else {
        Write-Host "⚠️ Dependencies not installed" -ForegroundColor Yellow
        Write-Host ""
        Write-Host "Next steps:" -ForegroundColor Cyan
        Write-Host "1. Download composer.phar" -ForegroundColor White
        Write-Host "2. Run: php.bat composer.phar install" -ForegroundColor White
    }
} else {
    Write-Host "❌ PHP installation failed" -ForegroundColor Red
}

Write-Host ""
Write-Host "Installation completed at: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor Gray
Write-Host "=========================" -ForegroundColor Green
